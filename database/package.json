{"name": "@flakiness/database", "version": "0.133.0", "type": "module", "private": true, "main": "./generated/db.js", "scripts": {"cli": "./lib/cli.js", "test": "npx playwright test -c playwright.config.ts --fully-parallel"}, "engines": {"node": ">=20"}, "exports": {".": {"types": "./types/src/index.d.ts", "import": "./lib/index.js", "require": "./lib/index.js"}}, "author": "Degu Labs, Inc", "license": "Fair Source 100", "dependencies": {"@flakiness/shared": "0.133.0", "@inquirer/confirm": "^5.0.0", "commander": "^12.1.0", "dotenv": "^16.4.5", "execa": "^9.4.0", "kysely": "^0.27.4", "ms": "^2.1.3", "pg": "^8.13.0"}, "devDependencies": {"@electric-sql/pglite": "^0.2.11", "@flakiness/report": "0.133.0", "@playwright/test": "^1.54.0", "@types/emscripten": "^1.39.13", "@types/lodash": "^4.17.7", "@types/lodash-es": "^4.17.12", "@types/pg": "^8.11.10", "kysely-codegen": "^0.17.0", "podkeeper": "^0.3.1"}}