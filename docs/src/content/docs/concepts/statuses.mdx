---
title: Test Statuses
sidebar:
  order: 3
---

import ThemeImage from '../../../components/ThemeImage.astro';

For any [timeline](/docs/concepts/timelines/), Flakiness.io computes test statuses for all runs, commits, and days.

## Test Status for Run

Let's say we have a test `testNervousSquirrel` which was executed as part
of some run.

Usually [Flakiness Report](/docs/concepts/flakiness-report) contains the test status, so Flakiness.io uses just this:

import TestRunDark from '../../../assets/flakiness-test-run-dark.svg';
import TestRunLight from '../../../assets/flakiness-test-run-light.svg';

<ThemeImage 
  light={TestRunLight}
  dark={TestRunDark}
  alt="Test statuses for a run"
/>

:::note
Oftentimes, testrunners are configured to retry failed tests. If a test failed 
and then passed, then this test is marked as **Flaked** for the run.
:::


## Test Status for Commit

As discussed [previously](/docs/concepts/commits/), all runs in Flakiness.io are attributed with a `CommitId`.
So let's say we now have a few runs for the same commit that belong to the same [timeline](/docs/concepts/timelines/).


import StatusesTestCommitDark from '../../../assets/flakiness-test-commit-dark.svg';
import StatusesTestCommitLight from '../../../assets/flakiness-test-commit-light.svg';

<ThemeImage 
  light={StatusesTestCommitLight}
  dark={StatusesTestCommitDark}
  alt="Test statuses for a commit"
/>

In this particular case, Flakiness.io deduces that test `testNervousSquirrel` is **flaky** in the **Windows Timeline**
for the **commit D**, since the same test both **passed** and **failed**.

In general, a test status for a commit and timeline is defined like this:

| Status | Description |
|--------|-------------|
| <span style="white-space: nowrap;">∅ skipped</span> | The test has only *Skipped* runs |
| <span style="white-space: nowrap;">✅ passed</span> | The test has only *Passed* runs and possibly some *Skipped* runs |
| <span style="white-space: nowrap;">❌ failed</span> | The test has only *Failed* runs and possibly some *Skipped* runs |
| <span style="white-space: nowrap;">⚠️ flaked</span> | Either the test has a *Flaked* run, or 1+ *Passed* and 1+ *Failed* runs. |


## Test Status for Multiple Commits

Now let's say we're tasked with computing test status for a bunch of commits:

import StatusesTestHistoryDark from '../../../assets/flakiness-test-history-dark.svg';
import StatusesTestHistoryLight from '../../../assets/flakiness-test-history-light.svg';

<ThemeImage 
  light={StatusesTestHistoryLight}
  dark={StatusesTestHistoryDark}
  alt="Test statuses for a History"
/>

Flakiness.io computes status of a test for given commits and a timeline based on its last known commit result.
However, *the test will only be marked as "passed" if it's last run was "passed", and there is **no failed or flaked commit**.*

In case of Windows timeline across these 4 commits, the test `testNervousSquirrel` will be marked as "flaky".

So the test status in a commit history can formalized like this:

| Status | Description |
|--------|-------------|
| <span style="white-space: nowrap;">∅ skipped</span> | Test's last tested commit has "skipped" status |
| <span style="white-space: nowrap;">❌ failed</span> | Tast's last tested commit has "failed" status |
| <span style="white-space: nowrap;">✅ passed</span> | Test's last tested commit has "passed" status, and no commit in history has either "failed" or "flaked" status |
| <span style="white-space: nowrap;">⚠️ flaked</span> | Either test's last tested commit has "flaked" status, or it has "passed" status, but the history has test failures. |

## Test Status for Day

While source code evolves with commits, it is usually more convenient to perceive
test history in terms of days.

Flakiness.io uses **user's timezone** to split commit history into sub-histories per each day,
and computes test status for each of these histories:

import StatusesTestDailyDark from '../../../assets/flakiness-test-daily-dark.svg';
import StatusesTestDailyLight from '../../../assets/flakiness-test-daily-light.svg';

<ThemeImage 
  light={StatusesTestDailyLight}
  dark={StatusesTestDailyDark}
  alt={"Test history per day"}
/>

In our hypothetical example, we see that `testNervousSquirrel`:
- ❌ Ended the day of May 7, 2025 failing
- ⚠️ Was flaking on May 25, 2025
- ✅ And so far has perfect record Today, on May 26, 2025.