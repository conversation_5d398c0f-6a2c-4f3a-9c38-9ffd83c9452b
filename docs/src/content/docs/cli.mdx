---
title: CLI Tool
sidebar:
  order: 5
---

import { Steps, TabItem, Tabs } from '@astrojs/starlight/components';

Flakiness.io project maintains an official `flakiness` CLI tool to work with Flakiness Reports.


## Installation

Flakiness CLI tool can be installed on any platform:

<Tabs>
  <TabItem label="Mac & Linux">
    Install the `flakiness` CLI using the installation script:

    ```bash
    curl -LsSf https://cli.flakiness.io/install.sh | sh
    ```
  </TabItem>
  
  <TabItem label="Windows">
    Install the `flakiness` CLI using PowerShell:

    ```powershell
    pwsh -c "irm https://cli.flakiness.io/install.ps1 | iex"
    ```
  </TabItem>
  
  <TabItem label="NPM">
    The `flakiness` CLI tool also comes as part of the `@flakiness/sdk` NPM package:

    ```bash
    npm i @flakiness/sdk@latest
    ```
  </TabItem>
</Tabs>

## Viewing Reports

To view locally saved Flakiness report, run:

```bash
flakiness show
```

The command will:
- Start a local server serving the report
- Automatically open the report in your default browser
- Display the URL where the report is accessible

Press `Ctrl+C` to stop the server.

**Example:**
```bash
flakiness show
flakiness show ./fkrun-123
```

## Linking

Local Git checkout can be linked to a Flakiness.io project. In this case, local reports will be
shown together with the cloud analytics from Flakiness.io.

To link local project checkout to Flakiness.io project:

<Steps>
1. Navigate to the root of your project checkout on the file system
2. Run the `link` command with the URL of the flakiness.io project. For example:
    ```bash
    flakiness link https://flakiness.io/flakiness/pytest-flakiness
    ```
</Steps>


## Upload Reports

Upload a Flakiness report to the flakiness.io service:

```bash
flakiness upload <relative-paths...>
```

**Options:**
- `<relative-paths...>` - Paths to the Flakiness report files (typically `report.json`)
- `-t, --access-token <token>` - A read-write flakiness.io access token (can also be set via `FLAKINESS_ACCESS_TOKEN` environment variable)
- `-e, --endpoint <url>` - An endpoint where the service is deployed (can also be set via `FLAKINESS_ENDPOINT` environment variable, defaults to `https://flakiness.io`)

The access token is typically auto-detected from your login session and project link, so you usually don't need to provide it manually.

**Example:**
```bash
flakiness upload ./flakiness-report/report.json
```

## Download Reports

Download Flakiness reports from the flakiness.io service:

```bash
flakiness download
```

**Options:**
- `--run-id <runId>` - Download a specific run by its ID
- `--since <date>` - Download all runs uploaded since the specified date
- `-j, --parallel <number>` - Number of parallel downloads (default: 1)

**Examples:**
```bash
# Download a specific run
flakiness download --run-id 123

# Download all runs since a date
flakiness download --since 2024-01-01

# Download with parallel processing
flakiness download --since 2024-01-01 --parallel 4
```

Downloaded reports are saved to `fkrun-<runId>/` directories, each containing:
- `report.json` - The Flakiness report file
- `attachments/` - Directory with all attachments (if any)

## Command Reference

The full list of commands and options is available via the command's help option:

```bash
flakiness --help
```
