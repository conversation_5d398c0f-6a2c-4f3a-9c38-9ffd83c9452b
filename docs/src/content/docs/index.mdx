---
title: What is Flakiness.io?
sidebar:
  order: 1
---

import { Steps, TabItem, Tabs } from '@astrojs/starlight/components';
import ThemeImage from '../../components/ThemeImage.astro';

:::tip[See it in action]
See Flakiness Dashboard in your browser: https://flakiness.io/flakiness/flakiness
:::


Flakiness.io is a **test analytics service** and **dashboard** that uses both **commit history** and **test execution history**
to accurately identify regressions and detect trends.

import IntroDark from '../../assets/intro-dark.svg';
import IntroLight from '../../assets/intro-light.svg';

<ThemeImage 
  light={IntroLight}
  dark={IntroDark}
  alt="Detailed Architecture Diagram"
  width={800}
/>

Flakiness.io service can be [deployed on custom infrastructure](/docs/on-premise/overview/).
Official deployment is maintained by Degu Labs, Inc and is available at https://flakiness.io.



## Use Cases

Flakiness.io is designed to handle a wide range of use cases, including:

1. **End-to-end Browser Tests:** Flakiness.io fully supports all Playwright Test features and comes with a first-class
   [Playwright Test integration](/docs/integrations/playwright).
2. **Unit Tests:** Flakiness.io has custom analytics engine that effortlessly processes **tens of thousands** of test results
  across months of historic data. Flakiness.io supports [JUnit integration](/docs/integrations/junit) out-of-the-box.
3. **Performance Tests:** Flakiness.io analytics engine analyses duration trends and can be used to track & pinpoint performance changes.

Flakiness.io aggregates all test types onto a single dashboard, allowing teams to see
the big picture, or utilize [Flakiness Query Language](/docs/concepts/fql) and [Timelines](/docs/concepts/timelines) features to drill down and select only the tests
relevant to their specific subsystem.

:::tip[Designed for Monorepos]
Flakiness.io is ideal for monorepos, unifying the diverse testing frameworks and mixed stacks typical of large codebases.
:::

## Limitations

- Flakiness.io only supports https://github.com as a source of commit history.

