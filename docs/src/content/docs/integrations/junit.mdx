---
title: JUnit
sidebar:
  order: 5.75
---

JUnit XML files could be converted to Flakiness Report via the [CLI Tool](/docs/cli).
Once JUnit XML files are converted into a Flakiness Report, this report can be either [viewed locally](/docs/cli#viewing-reports) or [uploaded](/docs/cli#upload-reports).

To convert JUnit XML report(s) into a Flakiness report, run:

```bash
flakiness convert-junit <junit-root-dir-path>
```

**Options:**
- `<junit-root-dir-path>` - Path to JUnit XML file or directory containing XML files
- `--output-dir <dir>` - Output directory for the report (default: `flakiness-report`).
- `--env-name <name>` - Environment name for the report (default: `junit`)
- `--commit-id <id>` - Git commit ID (auto-detected if not provided)

The command will:
- Read JUnit XML files (single file or recursively from a directory)
- Auto-detect the git commit ID from your repository (unless `--commit-id` is provided)
- Generate a Flakiness report and save it as `report.json` in the specified output directory.

**Example:**
```bash
# Convert test results XML files into a Flakiness report.
flakiness convert-junit ./test-results --env-name ci
flakiness upload ./flakiness-report
```
