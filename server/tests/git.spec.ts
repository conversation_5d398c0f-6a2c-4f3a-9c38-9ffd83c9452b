import { FlakinessReport } from '@flakiness/report';
import { xxHashObject } from '@flakiness/shared/common/utils.js';
import { expect, test } from '@playwright/test';
import assert from 'assert';
import { HeapSet } from '../src/common/heapSet.js';
import { Git } from '../src/node/git.js';

class MockGitProvider implements Git.Provider {
  private _commits = new Map<string, Git.Commit>();
  private _branchToSha = new Map<string, string>();

  constructor(private _defaultBranch: string) {
  }

  private _pushCommit(branchName: string, message: string, parents: string[] = []) {
    const branchSha = this._branchToSha.get(branchName);
    if (branchSha) {
      parents = parents.slice();
      parents.unshift(branchSha);
    }
    const currentBranchCommit = branchSha ? this._commits.get(branchSha) : undefined;
    const commit: Git.Commit = {
      commitId: '' as FlakinessReport.CommitId,
      message,
      parents: parents as FlakinessReport.CommitId[],
      timestamp: (currentBranchCommit ? currentBranchCommit.timestamp + 1 : 1000) as FlakinessReport.UnixTimestampMS,
      walkIndex: 0,
    }
    commit.commitId = xxHashObject(commit) as FlakinessReport.CommitId;
    this._commits.set(commit.commitId, commit);
    this._branchToSha.set(branchName, commit.commitId);
  }

  branch(name: string = this._defaultBranch) {
    const branch = {
      name,

      push: (commitMsgs: string[]) => {
        for (const msg of commitMsgs)
          this._pushCommit(name, msg);
        return branch;
      },

      fork: (newName: string) => {
        const sha = this._branchToSha.get(name);
        assert(sha);
        this._branchToSha.set(newName, sha);
        return this.branch(newName);
      },

      head: () => {
        return this._branchToSha.get(name);
      },

      mergeInto: (otherBranch: string) => {
        const headSha = this._branchToSha.get(name);
        const baseSha = this._branchToSha.get(otherBranch);
        assert(headSha)
        assert(baseSha);
        this._pushCommit(otherBranch, `Merge ${headSha} into ${baseSha}`, [headSha]);
        return branch;
      }
    }
    return branch;
  }

  async *listPullRequests(options: { sort: 'created' | 'updated'; since?: Date; }, signal: AbortSignal | undefined): AsyncGenerator<Git.PullRequest[]> {
  }

  async defaultBranch(signal: AbortSignal | undefined): Promise<string> {
    return this._defaultBranch;
  }

  async *listBranches(signal: AbortSignal | undefined): AsyncGenerator<{ name: string; sha: string; }[]> {
    for (const [name, sha] of this._branchToSha)
      yield [{ name, sha }];
  }

  async *listCommits(options: { sha: string; since?: Date; }, signal: AbortSignal | undefined): AsyncGenerator<Git.Commit[]> {
    let sha = this._branchToSha.get(options.sha) ?? options.sha;
    let commit = this._commits.get(sha);
    if (!commit)
      return;
    const heap = HeapSet.createMax<Git.Commit>();
    heap.add(commit, commit.timestamp);
    while (heap.size) {
      const commit = heap.pop()!;
      yield [commit];
      for (const parent of commit.parents)
        heap.add(this._commits.get(parent)!, this._commits.get(parent)!.timestamp);
    }
  }
}

test('should work', async () => {
  const provider = new MockGitProvider('main');
  const main = provider.branch('main');
  main.push([
    'staging',
    'first',
    'second',
    'third',
  ]);

  const git = Git.Repository.createEmpty();
  await git.fetch(provider, []);

  expect(git.branches().map(ref => ref.name)).toEqual(['main']);
  expect(git.iterator('main').collect({}).length).toBe(4);
});


test('should support branches', async () => {
  const provider = new MockGitProvider('main');
  const main = provider.branch('main');
  main.push([
    'staging',
    'first',
    'second',
    'third',
  ]);

  const feature = main.fork('feature');
  feature.push([
    'feature-1',
    'feature-2',
  ]);

  const git = Git.Repository.createEmpty();
  await git.fetch(provider, []);

  expect(git.branches().map(ref => ref.name)).toEqual(['main', 'feature']);
  expect(git.iterator('main').collect({}).length).toBe(4);
  expect(git.iterator('feature').collect({}).length).toBe(6);
});

test('should support merge commits', async () => {
  const provider = new MockGitProvider('main');
  const main = provider.branch('main');
  main.push([
    'staging',
    'first',
    'second',
    'third',
  ]);

  const feature = main.fork('feature');
  feature.push([
    'feature-1',
    'feature-2',
  ]);

  feature.mergeInto('main');

  const git = Git.Repository.createEmpty();
  await git.fetch(provider, []);

  expect(git.branches().map(ref => ref.name)).toEqual(['main', 'feature']);
  expect(git.iterator('main').collect({}).length).toBe(7);
  expect(git.iterator('feature').collect({}).length).toBe(6);
});

test('should support merge commit serialization / deserialization', async () => {
  const provider = new MockGitProvider('main');
  const main = provider.branch('main');
  main.push([
    'staging',
    'first',
    'second',
    'third',
  ]);

  const foo = main.fork('foo');
  foo.push([
    'foo-1',
    'foo-2',
  ]);

  const bar = foo.fork('bar');
  bar.push([
    'bar-1',
    'bar-2',
  ]);

  bar.mergeInto('foo');
  foo.mergeInto('main');

  // serialize repository and then deserialize right away.
  let git = Git.Repository.createEmpty();
  await git.fetch(provider, []);

  git = Git.Repository.deserialize(git.serialize())!;

  expect(git.branches().map(ref => ref.name)).toEqual(['main', 'foo', 'bar']);
  expect(git.iterator('main').collect({}).length).toBe(10);
});

test('should support time selection', async () => {
  const provider = new MockGitProvider('main');
  const main = provider.branch('main');
  main.push([
    'staging',// ts=1000
    'first',  // ts=1001
    'second', // ts=1002
    'third',  // ts=1003
    'forth',  // ts=1004
    'fifth',  // ts=1005
    'sixth',  // ts=1006
  ]);

  // serialize repository and then deserialize right away.
  let git = Git.Repository.createEmpty();
  await git.fetch(provider, []);

  const it = git.iterator('main');
  expect(it.collect({
    sinceTimestamp: 1008 as FlakinessReport.UnixTimestampMS,
    untilTimestamp: 1010 as FlakinessReport.UnixTimestampMS,
  }).length).toBe(0);
  expect(it.collect({
    sinceTimestamp: 1006 as FlakinessReport.UnixTimestampMS,
    untilTimestamp: 1008 as FlakinessReport.UnixTimestampMS,
  }).length).toBe(1);
  expect(it.collect({
    sinceTimestamp: 1004 as FlakinessReport.UnixTimestampMS,
    untilTimestamp: 1006 as FlakinessReport.UnixTimestampMS,
  }).length).toBe(2);

  // Test if we can skip some commits first.
  const it2 = git.iterator('main');
  expect(it2.collect({
    sinceTimestamp: 1004 as FlakinessReport.UnixTimestampMS,
    untilTimestamp: 1006 as FlakinessReport.UnixTimestampMS,
  }).length).toBe(2);
  expect(it2.collect({}).length).toBe(4)
});

