{"name": "@flakiness/server", "private": true, "version": "0.133.0", "description": "", "type": "module", "types": "./types/src", "main": "./lib", "keywords": [], "author": "", "license": "ISC", "scripts": {"test": "npx playwright test -c playwright.config.ts"}, "exports": {"./*": {"types": "./types/src/*", "import": "./lib/*", "require": "./lib/*"}}, "dependencies": {"@aws-sdk/client-s3": "3.460.0", "@aws-sdk/s3-request-presigner": "3.460.0", "@flakiness/database": "0.133.0", "@flakiness/report": "0.133.0", "@flakiness/shared": "0.133.0", "body-parser": "^1.20.2", "chalk": "^5.3.0", "commander": "^12.1.0", "compression": "^1.8.1", "cookie-parser": "^1.4.6", "debug": "^4.4.1", "express": "^4.18.2", "express-async-errors": "^3.1.1", "jsonwebtoken": "^9.0.2", "ms": "^2.1.3", "octokit": "^3.1.2", "pg": "^8.16.0", "pg-format": "^1.0.4", "prom-client": "^15.0.0", "stripe": "^17.4.0", "supports-color": "^10.0.0", "vlq": "^2.0.4", "zod": "^3.25.23"}, "devDependencies": {"@types/compression": "^1.8.1", "@types/cookie-parser": "^1.4.5", "@types/express": "^4.17.20", "@types/jsonwebtoken": "^9.0.4", "@types/ms": "^0.7.34", "@types/pg": "^8.15.2", "@types/pg-format": "^1.0.5", "@types/ws": "^8.18.1", "glob": "^10.3.10", "podkeeper": "^0.3.1"}}