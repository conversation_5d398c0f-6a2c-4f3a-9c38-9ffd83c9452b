import { xxHash } from "@flakiness/shared/common/utils.js";
import { SyncComputationCache } from "../computationCache.js";
import { Ranges } from "../ranges.js";
import { Timeline } from "../timeline/timeline.js";
import { WireTypes } from "../wireTypes.js";
import { CommitAnalyzer, ContributionReport, createCommitContributionReport } from "./commitAnalyzer.js";
import { Histogram as H } from './histogram.js';
import { Stats as S } from "./stats.js";
import { TestOutcomes as T } from './testOutcomes.js';

/**
 * Span Analyzer holds an ordered set of commits and computes data
 * for them.
 */

export type SpanStats = {
  testOutcomes: T.TestOutcomes,
  durations: H.Histogram[],
  commitsCount: number,
}

export class SpanAnalyzer {
  static etag(commits: CommitAnalyzer[]): string {
    return xxHash(commits.map(c => c.etag()));
  }

  private _etag: string;

  private _contributions = new SyncComputationCache<Timeline, ContributionReport>({
    size: Timeline.MAX_TIMELINES,
    etag: timeline => timeline.etag(),
    compute: timeline => {
      return createCommitContributionReport(this.commits.map(commit => commit.contributions(timeline)).flat());
    }
  });

  private _testOutcomes = new SyncComputationCache<Timeline, T.TestOutcomes>({
    size: Timeline.MAX_TIMELINES,
    etag: timeline => timeline.etag(),
    compute: timeline => {
      const unhealthyTests = this.unhealthyTests(timeline);
      const outcomes = T.unionAll(this._contributions.get(timeline).map(c => c.outcomes));
      // On a span level, we consider a test "passing" only if it had no issues throughout the day.
      outcomes.flaked = Ranges.union(outcomes.flaked, unhealthyTests);
      T.normalizeInplace(outcomes);
      return outcomes;
    }
  });

  private _durations = new SyncComputationCache<Timeline, H.Histogram[]>({
    size: Timeline.MAX_TIMELINES,
    etag: timeline => timeline.etag(),
    compute: timeline => {
      const contribs = this._contributions.get(timeline);
      return CommitAnalyzer.computeDurationBuckets(timeline, contribs);
    }
  });

  private _unhealthyTests = new SyncComputationCache<Timeline, T.TestRanges[]>({
    size: Timeline.MAX_TIMELINES,
    etag: timeline => timeline.etag(),
    compute: timeline => {
      let unhealthyTests: T.TestRanges = [] as any;
      const result: T.TestRanges[] = [];
      for (const commit of this.commits.toReversed()) {
        unhealthyTests = Ranges.union(unhealthyTests, T.unhealthyTests(commit.testOutcomes(timeline)));
        result.push(unhealthyTests);
      }
      return result.toReversed();
    }
  });

  constructor(public readonly commits: CommitAnalyzer[], etag: string = SpanAnalyzer.etag(commits)) {
    this._etag = etag;
  }

  etag() {
    return this._etag;
  }

  testOutcomes(timeline: Timeline) {
    return this._testOutcomes.get(timeline);
  }

  durations(timeline: Timeline): H.Histogram[] {
    return this._durations.get(timeline);
  }

  runEnvironments(out = new Map<S.EnvId, WireTypes.RunEnvironment>()): Map<S.EnvId, WireTypes.RunEnvironment> {
    for (const c of this.commits)
      c.runEnvironments(out);
    return out;
  }

  parentCommit(commitId: S.CommitId): S.CommitId|undefined {
    const idx = this.commits.findIndex(c => c.commitId === commitId);
    if (idx === -1 || idx +1 >= this.commits.length)
      return undefined;
    return this.commits[idx + 1].commitId;
  }

  unhealthyTests(timeline: Timeline, commitId?: S.CommitId): T.TestRanges {
    if (!this.commits.length)
      return T.EMPTY_TESTS;
    if (!commitId)
      return this._unhealthyTests.get(timeline)[0];
    const idx = this.commits.findIndex(c => c.commitId === commitId);
    if (idx === -1)
      return T.EMPTY_TESTS
    return this._unhealthyTests.get(timeline)[idx];
  }
}