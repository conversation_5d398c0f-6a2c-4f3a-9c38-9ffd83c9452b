import { Cache, SharedCacheStore } from "./caches/cache.js";
import { LRUPolicy } from "./caches/lruPolicy.js";
import { Singleflight } from "./singleflight.js";

export class ComputationCache<INPUT, OUTPUT extends {}> {
  private _singleflight: Singleflight<INPUT, OUTPUT>;
  private _cache: Cache<string, OUTPUT>;

  constructor(private _options: {
    cache?: SharedCacheStore,
    size: number,
    etag: (input: INPUT) => string,
    compute: (input: INPUT, etag: string, signal: AbortSignal) => Promise<OUTPUT>,
    shouldCache?: (value: OUTPUT, durationMs: number) => true,
    onGetTiming?: (input: INPUT, key: string, since: number, until: number, cacheHit: boolean) => void,
  }) {
    this._cache = new Cache(new LRUPolicy(this._options.size), this._options.cache);
    this._singleflight = new Singleflight({
      key: this._options.etag,
      fetch: async (input: INPUT, key: string, signal: AbortSignal) => {
        const start = performance.now();
        const value = await this._options.compute(input, key, signal);
        const end = performance.now();
        if (this._options.shouldCache?.(value, end - start) ?? true)
          this._cache.set(key, value);
        return value;
      },
    });
  }

  cacheSize() {
    return this._cache.size;
  }

  async get(input: INPUT): Promise<OUTPUT> {
    const key = this._options.etag(input);
    // We avoid the `using` keyword for now in javascript, since it's too fresh.
    const since = performance.now();
    if (this._cache.has(key)) {
      const result = this._cache.get(key)!;
      this._options.onGetTiming?.(input, key, since, performance.now(), true);
      return result;
    }
    const result = (await this._singleflight.fetch(input, key))!;
    this._options.onGetTiming?.(input, key, since, performance.now(), false);
    return result;
  }
}

export class SyncComputationCache<INPUT, OUTPUT extends {}, KEY = string> {
  private _cache: Cache<KEY, OUTPUT>;

  constructor(private _options: {
    cache?: SharedCacheStore,
    size: number,
    etag: (input: INPUT) => KEY,
    compute: (input: INPUT, etag: KEY) => OUTPUT,
    shouldCache?: (value: OUTPUT, durationMs: number) => true,
    onGetTiming?: (input: INPUT, key: KEY, since: number, until: number, cacheHit: boolean, cacheSize: number) => void,
  }) {
    this._cache = new Cache(new LRUPolicy(this._options.size), this._options.cache);
  }

  cacheSize() {
    return this._cache.size;
  }

  get(input: INPUT): OUTPUT {
    const since = performance.now();
    const key = this._options.etag(input);
    let value = this._cache.get(key);
    if (!value) {
      const start = performance.now();
      value = this._options.compute(input, key);
      if (this._options.shouldCache?.(value, performance.now() - start) ?? true)
        this._cache.set(key, value);
      this._options.onGetTiming?.(input, key, since, performance.now(), false, this._cache.size);
    } else {
      this._options.onGetTiming?.(input, key, since, performance.now(), true, this._cache.size);
    }
    return value;
  }
}
