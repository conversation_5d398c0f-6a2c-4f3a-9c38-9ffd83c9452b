import assert from "assert";
import debug from "debug";
import ms from "ms";
import v8 from 'v8';
import { SharedCacheStore } from "../common/caches/cache.js";
import { LRUPolicy } from "../common/caches/lruPolicy.js";

const log = debug('fk:heap');

type EvictionMode = {
  /**
   * Heap utilization threshold (0-1) where this mode activates.
   */
  threshold: number;
  /**
   * Hysteresis buffer in percentage points - how much below threshold before deactivating.
   */
  hysteresis: number;
  /**
   * Fraction of cache bytes to evict (0-1).
   */
  evictionRatio: number;
  /**
   * How often to sample heap in this mode
   */
  samplingIntervalMs?: number;
};

function assertClamp(x: number, segment: [number, number], name: string) {
  assert(segment[0] <= x && x <= segment[1], `Expected ${name} to be between ${segment[0]} and ${segment[1]}, found ${x}`);
}

export interface HeapGuardOptions {
  lowWatermark: EvictionMode;
  highWatermark: EvictionMode;
  emergencyThreshold: number;

  defaultSamplingIntervalMs: number;
}

type GuardMode = 'normal' | 'low' | 'high';

function validateMode(mode: EvictionMode, name: string) {
  assertClamp(mode.threshold, [0, 1], `${name}.threshold`);
  assertClamp(mode.hysteresis, [0, 1], `${name}.hysteresis`);
  assertClamp(mode.evictionRatio, [0, 1], `${name}.evictionRatio`);
}

export class ManagedMemoryCache {
  static createWithDefaultSettings() {
    // Otherwise, rely on percentage.
    return new ManagedMemoryCache({
      defaultSamplingIntervalMs: ms('2 seconds'),
      lowWatermark: {
        threshold: 0.65, // 65% is a low watermark. We start dropping entries from cache.
        // 20% of entries are evicted at this stage. 
        // Sampling is slowed down to allow GC to kick in.
        evictionRatio: 0.20,
        hysteresis: 0.15,
        samplingIntervalMs: ms('10 seconds'),
      },
      highWatermark: {
        threshold: 0.75, // 75% is a high watermark. Start sampling & dropping more aggressively.
        evictionRatio: 0.5, // 50% of entries are evicted.
        hysteresis: 0.25, // until it drops under 50% heap utilization.
        samplingIntervalMs: ms('10 seconds'),
      },
      emergencyThreshold: 0.8, // 80% is emergency. Cache admission is stopped at this point.
    });
  }

  private _store: SharedCacheStore;
  private _currentMode: GuardMode = 'normal';
  private _samplingInterval: number = 1000;

  private _lastSample: HeapSample = sampleHeap();
  private _timer?: NodeJS.Timeout;
  private _admission: boolean = true;

  constructor(private _options: HeapGuardOptions) {
    assert(this._options.lowWatermark.threshold < this._options.highWatermark.threshold, 'Low watermark threshold must be less than high watermark threshold');    
    assert(this._options.highWatermark.threshold < this._options.emergencyThreshold, 'Emergency threshold must be higher than high watermark threshold');

    validateMode(this._options.lowWatermark, 'Low watermark');
    validateMode(this._options.highWatermark, 'High watermark');
    assertClamp(this._options.emergencyThreshold, [0, 1], 'emergencyThreshold');

    this._store = new SharedCacheStore({ 
      policy: new LRUPolicy(Infinity),
      shouldAdmit: this._shouldAdmit.bind(this),
    });

    this._tick();
    this._log(this._lastSample);
  }

  status() {
    return this._lastSample;
  }

  private _shouldAdmit() {
    this._maybeSampleHeap();
    return this._admission;
  }

  private _computeNewMode({ utilization }: HeapSample): GuardMode {
    const targetMode = utilization >= this._options.highWatermark.threshold ? 'high' :
        utilization >= this._options.lowWatermark.threshold ? 'low' :
        'normal';
    // From normal mode, we can jump to anything, really.
    if (this._currentMode === 'normal')
      return targetMode;
    // If current mode is `low`, and it gets escalated to `high`, then escalate.
    if (this._currentMode === 'low' && targetMode === 'high')
      return 'high';
    // Otherwise, we allow dropping to the target mode only if we're below hysterezis of the current mode.
    const { threshold, hysteresis } = this._currentMode === 'high' ? this._options.highWatermark : this._options.lowWatermark;
    return utilization < threshold - hysteresis ? targetMode : this._currentMode;
  }

  private _log(sample: HeapSample, evictCount: number = 0) {
    let pressure = 'low';
    if (this._currentMode === 'low')
      pressure = 'med';
    else if (this._currentMode === 'high')
      pressure = 'high';
    const tokens = [stringifySample(sample)];
    tokens.push(`pressure: ${pressure}`);
    if (evictCount)
      tokens.push(`evicted: ${evictCount}`);
    if (!this._admission)
      tokens.push(`NO ADMISSIONS`);
    log(tokens.join(' '));
  }

  private _tick() {
    this._maybeSampleHeap();
    this._timer = setTimeout(() => this._tick(), this._samplingInterval);
  }

  private _maybeSampleHeap(): void {
    // Sample heap not more often than sampling interval.
    const now = Date.now();
    if (this._lastSample.timestamp + this._samplingInterval > now)
      return;

    this._lastSample = sampleHeap();
    this._admission = (this._lastSample.utilization < this._options.emergencyThreshold);
    const oldMode = this._currentMode;
    this._currentMode = this._computeNewMode(this._lastSample);

    // Evict entries according to the current mode.
    if (this._currentMode === 'normal') {
      // In normal mode, we log utilization only if we transitioned into it.
      if (this._currentMode !== oldMode)
        this._log(this._lastSample);
      this._samplingInterval = this._options.defaultSamplingIntervalMs;
      return;
    }

    const config = this._currentMode === 'high' ? this._options.highWatermark : this._options.lowWatermark;
    this._samplingInterval = config.samplingIntervalMs ?? this._options.defaultSamplingIntervalMs;

    const targetSize = this._store.size * (1 - config.evictionRatio);
    // Make sure there's no inifinite loop.
    let evictCount = 0;
    for (let N = this._store.size; evictCount < N && this._store.size > targetSize; ++evictCount)
      this._store.evict();

    this._log(this._lastSample, evictCount);
  }

  dispose() {
    clearTimeout(this._timer);
  }

  sharedCacheStore(): SharedCacheStore {
    return this._store;
  }
}

function mb(bytes: number) {
  const suffixes = ['B', 'KB', 'MB', 'GB'];
  for (const suffix of suffixes) {
    if (bytes < 1024)
      return bytes.toFixed(2) + suffix;
    bytes /= 1024;
  }
  return bytes.toFixed(2) + 'PB';
}

type HeapSample = {
  utilization: number,
  usedBytes: number,
  totalBytes: number,
  timestamp: number,
}

function sampleHeap(): HeapSample {
  const heapStats = v8.getHeapStatistics();
  const usedBytes = heapStats.used_heap_size;
  const totalBytes = heapStats.heap_size_limit;

  return {
    timestamp: Date.now(),
    usedBytes,
    totalBytes,
    utilization: usedBytes / totalBytes,
  }
}

function stringifySample(sample: HeapSample): string {
  return `${(sample.utilization * 100).toFixed(2)}% (${mb(sample.usedBytes)}/${mb(sample.totalBytes)})`;
}