import { ActiveJob, ArchivedJob, Database, DatabaseConfig, NewOrganization, NewProductPlan, NewUser, OrgAccessRole, Organization, OrganizationUpdate, OrgId, ProductPlan, ProductPlanId, ProductPlanUpdate, Project, ProjectAccessRole, ProjectId, ProjectUpdate, ProjectVisibility, QueuedJob, SourceAuthType, User, UserId, UserSession } from '@flakiness/database';
import { FlakinessReport } from '@flakiness/report';
import { xxHashObject } from '@flakiness/shared/common/utils.js';
import ms from 'ms';

import { SingleflightCache } from '../common/singleflightcache.js';
import { WireTypes } from '../common/wireTypes.js';
import { Config } from './configuration.js';
import { ServerTiming } from './serverTiming.js';
import { XNotify } from './xnotify.js';

const DEFAULT_TTL = ms('5sec');

function cachedCall<ARG extends any[], RET>(raw: (...arg: ARG) => Promise<RET>, options: {
  init?: (cache: SingleflightCache<ARG, { value: RET }>) => any,
  default: RET,
  serverTiming: ServerTiming,
}): { (...arg: ARG): Promise<RET> } {
  const cache = new SingleflightCache<ARG, { value: RET }>({
    ttl: DEFAULT_TTL,
    size: 100,
    // make sure database concurrency is infinite since we have a pool underneath that
    // already manages concurrency.
    concurrency: Infinity,
    key: arg => xxHashObject(arg),
    onGetTiming: (input, key, method, since, until) => options.serverTiming.recordTraceEvent('db', since, until),
    fetch: async (arg, key) => {
      const value = await raw(...arg);
      // We want to cache all values - even if they're "undefined".
      // This way, if we query database for existance of some element, we wouldn't need
      // to re-query until either TTL hits or a notification arrives.
      return { value };
    },
  });
  options?.init?.call(null, cache);
  return (...arg: ARG) => cache.get(arg).then(x => x?.value ?? options.default);
}

type MethodsOnly<T> = {
  [K in keyof T]: T[K] extends (...args: any[]) => any ? K : never;
}[keyof T];

type PublicMethods<T> = Pick<T, MethodsOnly<T>>;

export class CachedDatabase {
  static async configFromEnvOrDie(): Promise<DatabaseConfig> {
    return await Config.fromEnvironmentOrDie('Database configuration', async env => ({
      host: await env.text({
        env: 'PGHOST',
        description: ``,
        required: true,
      }),
      port: await env.integer({
        env: 'PGPORT',
        description: ``,
        required: true,
      }),
      user: await env.secret({
        env: 'PGUSER',
        description: ``,
        required: true,
      }),
      password: await env.secret({
        env: 'PGPASSWORD',
        description: ``,
        required: true,
      }), 
      database: await env.secret({
        env: 'PGDATABASE',
        description: ``,
        required: true,
      }),
      encryptionKey: await env.secret({
        env: 'DB_ENCRYPTION_KEY',
        description: ``,
        required: true,
      }),
    }));
  }

  static async initializeFromConfig(xnotify: XNotify, serverTiming: ServerTiming) {
    const pgsqlConfig = await CachedDatabase.configFromEnvOrDie();
    const uncachedDb = await Database.initializeOrDie(pgsqlConfig);
    return new CachedDatabase(uncachedDb, xnotify, serverTiming);
  }

  readonly users: PublicMethods<typeof Database.prototype.users>;
  readonly orgs: PublicMethods<typeof Database.prototype.orgs>;
  readonly orgSharing: PublicMethods<typeof Database.prototype.orgSharing>;
  readonly projects: PublicMethods<typeof Database.prototype.projects>;
  readonly projectSharing: PublicMethods<typeof Database.prototype.projectSharing>;
  readonly projectSource: PublicMethods<typeof Database.prototype.projectSource>;
  readonly queues: PublicMethods<typeof Database.prototype.queues>;
  readonly productPlans: PublicMethods<typeof Database.prototype.productPlans>;
  readonly userSessions: PublicMethods<typeof Database.prototype.userSessions>;

  constructor(
    private _db: Database,
    private _xnotify: XNotify,
    serverTiming: ServerTiming,
  ) {
    const db = this._db;
    const xnotify = this._xnotify;

    const msg = {
      user: {
        updated: 'user-updated',
        created: 'user-created',
        deleted: 'user-deleted',
      },
      userSession: {
        deleted: 'user-session-deleted',
      },
      org: {
        updated: 'org-updated',
        created: 'org-created',
        deleted: 'org-deleted',
      },
      project: {
        updated: 'project-updated',
        created: 'project-created',
        deleted: 'project-deleted',
      },
      projectSharing: {
        changed: 'projectsharing-changed',
      },
      orgSharing: {
        changed: 'orgsharing-changed',
      },
      projectSource: {
        changed: 'projectsource-changed',
      },
      githubOauthUserTokens: {
        changed: 'githuboauthusertokens-changed',
      },
      projectMetrics: {
        changed: 'projectmetrics-changed',
      },
      productPlans: {
        created: 'productplans-created',
        updated: 'productplans-updated',
        deleted: 'productplans-deleted',
      },
    }

    this.users = {
      create: async (newUser: NewUser) => {
        using timing = serverTiming.start('db');
        const user = await db.users.create(newUser);
        user && await xnotify.notify(msg.user.created, user);
        return user;
      },
      findUsers: cachedCall(db.users.findUsers.bind(db.users), {
        init: cache => xnotify.subscribeAll<User>([msg.user.created], async user => cache.clear()),
        default: [],
        serverTiming,
      }),
      getOrganizations: cachedCall(db.users.getOrganizations.bind(db.users), {
        init: cache => xnotify.subscribeAll<Organization>([msg.org.created, msg.org.deleted], async org => void cache.refresh([org.owner_id], { dropCached: true })),
        default: [],
        serverTiming,
      }),
      get: cachedCall(db.users.get.bind(db.users), { default: undefined, serverTiming }),
      getByGithubId: cachedCall(db.users.getByGithubId.bind(db.users), {
        init: cache => xnotify.subscribeAll<User>([msg.user.created], async user => void (user.github_id ? cache.refresh([user.github_id], { dropCached: true }) : undefined)),
        default: undefined,
        serverTiming,
      }),
      getByPublicId: cachedCall(db.users.getByPublicId.bind(db.users), { default: undefined, serverTiming }),
    };

    this.userSessions = {
      createSession: async options => {
        using timing = serverTiming.start('db');
        const session = await db.userSessions.createSession(options);
        return session;
      },
      dropSession: async userSessionId => {
        using timing = serverTiming.start('db');
        const session = await db.userSessions.dropSession(userSessionId);
        session && await xnotify.notify(msg.userSession.deleted, session);
        return session;
      },
      dropSessionByTokenHash: async tokenHash => {
        using timing = serverTiming.start('db');
        const session = await db.userSessions.dropSessionByTokenHash(tokenHash);
        session && await xnotify.notify(msg.userSession.deleted, session);
        return session;
      },
      recordSessionAccess: async (sessionId, options) => {
        using timing = serverTiming.start('db');
        await db.userSessions.recordSessionAccess(sessionId, options);
      },
      listSessions: async (userId) => {
        // This is rarely used, so do not cache it.
        using timing = serverTiming.start('db');
        return await this._db.userSessions.listSessions(userId);
      },
      getByTokenHash: cachedCall(db.userSessions.getByTokenHash.bind(db.userSessions), {
        default: undefined,
        serverTiming,
        init: cache => xnotify.subscribeAll<UserSession>([msg.userSession.deleted], async session => void cache.delete([session.token_sha256]))
      }),
      getByPublicId: cachedCall(db.userSessions.getByPublicId.bind(db.userSessions), {
        default: undefined,
        serverTiming,
        init: cache => xnotify.subscribeAll<UserSession>([msg.userSession.deleted], async session => void cache.delete([session.session_public_id]))
      }),
    };

    this.orgs = {
      create: async (newOrg: NewOrganization) => {
        using timing = serverTiming.start('db');
        const org = await db.orgs.create(newOrg);
        org && await xnotify.notify(msg.org.created, org);
        return org;
      },
      update: async(orgId: OrgId, update: OrganizationUpdate) => {
        using timing = serverTiming.start('db');
        const org = await db.orgs.update(orgId, update);
        org && await xnotify.notify(msg.org.updated, org);
        return org;
      },
      delete: async (orgId: OrgId) => {
        using timing = serverTiming.start('db');
        const org = await db.orgs.delete(orgId);
        org && await xnotify.notify(msg.org.deleted, org);
        return org;
      },
      get: cachedCall(db.orgs.get.bind(db.orgs), {
        serverTiming,
        default: undefined,
        init: cache => xnotify.subscribeAll<Organization>([msg.org.created, msg.org.updated, msg.org.deleted], async org => void cache.refresh([org.org_id], { dropCached: true })),
      }),
      // All is uncached; it doesn't make sense to cache.
      all: async () => {
        using timing = serverTiming.start('db');
        return await this._db.orgs.all();
      },
      getBySlug: cachedCall(db.orgs.getBySlug.bind(db.orgs), {
        default: undefined,
        serverTiming,
        init: cache => xnotify.subscribeAll<Organization>([msg.org.created, msg.org.updated, msg.org.deleted], async org => void cache.refresh([org.org_slug], { dropCached: true })),
      }),
      getByPublicId: cachedCall(db.orgs.getByPublicId.bind(db.orgs), {
        default: undefined,
        serverTiming,
        init: cache => xnotify.subscribeAll<Organization>([msg.org.created, msg.org.updated, msg.org.deleted], async org => void cache.refresh([org.org_public_id], { dropCached: true })),
      }),
      getProjects: cachedCall(db.orgs.getProjects.bind(db.orgs), {
        default: [],
        serverTiming,
        init: cache => xnotify.subscribeAll<Project>([msg.project.created, msg.project.updated, msg.project.deleted], async project => void cache.refresh([project.org_id], { dropCached: true })),
      }),
      getProjectPublicIds: cachedCall(db.orgs.getProjectPublicIds.bind(db.orgs), {
        default: [],
        serverTiming,
        init: cache => xnotify.subscribeAll<Project>([msg.project.created, msg.project.updated, msg.project.deleted], async project => void cache.refresh([project.org_id], { dropCached: true })),
      }),
    };
    this.projects = {
      // All is uncached; it doesn't make sense to cache.
      all: async () => {
        using timing = serverTiming.start('db');
        return await this._db.projects.all();
      },
      create: async (proj, auth) => {
        using timing = serverTiming.start('db');
        const project = await db.projects.create(proj, auth);
        project && await xnotify.notify(msg.project.created, project);
        return project;
      },
      delete: async (projectId) => {
        using timing = serverTiming.start('db');
        const project = await db.projects.delete(projectId);
        project && await xnotify.notify(msg.project.deleted, project);
        return project;
      },
      update: async (projectId: ProjectId, update: ProjectUpdate) => {
        using timing = serverTiming.start('db');
        const project = await db.projects.update(projectId, update);
        project && await xnotify.notify(msg.project.updated, project);
        return project;
      },
      get: cachedCall(db.projects.get.bind(db.projects), {
        default: undefined,
        serverTiming,
        init: cache => xnotify.subscribeAll<Project>([msg.project.created, msg.project.updated, msg.project.deleted], async project => void cache.refresh([project.project_id], { dropCached: true })),
      }),
      getByAccessToken: cachedCall(db.projects.getByAccessToken.bind(db.projects), {
        default: undefined,
        serverTiming,
        init: cache => xnotify.subscribeAll<Project>([msg.project.created, msg.project.updated, msg.project.deleted], async project => void cache.refresh([project.flakiness_access_token], { dropCached: true })),
      }),
      getByPublicId: cachedCall(db.projects.getByPublicId.bind(db.projects), {
        default: undefined,
        serverTiming,
        init: cache => xnotify.subscribeAll<Project>([msg.project.created, msg.project.updated, msg.project.deleted], async project => void cache.refresh([project.project_public_id], { dropCached: true })),
      }),
      getBySlug: cachedCall(db.projects.getBySlug.bind(db.projects), {
        default: undefined,
        serverTiming,
        init: cache => xnotify.subscribeAll<Project>([msg.project.created, msg.project.updated, msg.project.deleted], async project => void cache.refresh([project.org_id, project.project_slug], { dropCached: true })),
      }),
      incReportCount: async (projectId) => {
        using timing = serverTiming.start('db');
        // We keep this without cache busting - doesn't worth the husle.
        return await db.projects.incReportCount(projectId);
      },
    }
    this.projectSharing = {
      setAccess: async (projectId: ProjectId, userId: UserId, access: ProjectAccessRole | undefined) => {
        using timing = serverTiming.start('db');
        await db.projectSharing.setAccess(projectId, userId, access);
        await xnotify.notify(msg.projectSharing.changed, { projectId, userId });
      },
      getUsers: cachedCall(db.projectSharing.getUsers.bind(db.projectSharing), {
        init: cache => xnotify.subscribe<{ projectId: ProjectId }>(msg.projectSharing.changed, async change => void cache.refresh([change.projectId], { dropCached: true })),
        default: [],
        serverTiming,
      }),
      getProjects: cachedCall(db.projectSharing.getProjects.bind(db.projectSharing), {
        init: cache => xnotify.subscribe<{ userId: UserId }>(msg.projectSharing.changed, async change => void cache.refresh([change.userId], { dropCached: true })),
        default: [],
        serverTiming,
      }),
    };
    this.orgSharing = {
      setAccess: async (orgId: OrgId, userId: UserId, access: OrgAccessRole | undefined) => {
        using timing = serverTiming.start('db');
        await db.orgSharing.setAccess(orgId, userId, access);
        await xnotify.notify(msg.orgSharing.changed, { orgId, userId });
      },
      getUsers: cachedCall(db.orgSharing.getUsers.bind(db.orgSharing), {
        serverTiming,
        default: [],
        init: cache => xnotify.subscribe<{ orgId: OrgId }>(msg.orgSharing.changed, async change => void cache.refresh([change.orgId], { dropCached: true })),
      }),
      getOrganizations: cachedCall(db.orgSharing.getOrganizations.bind(db.orgSharing), {
        serverTiming,
        default: [],
        init: cache => xnotify.subscribe<{ userId: UserId }>(msg.orgSharing.changed, async change => void cache.refresh([change.userId], { dropCached: true })),
      }),
    };

    this.projectSource = {
      setProjectSource: async (options: { projectId: ProjectId, sourceOwnerName: string, sourceRepo: string, personalAccessToken?: string, installationId?: string }) => {
        using timing = serverTiming.start('db');
        await db.projectSource.setProjectSource(options);
        await xnotify.notify(msg.projectSource.changed, options.projectId);
      },
      getPersonalAccessToken: cachedCall(db.projectSource.getPersonalAccessToken.bind(db.projectSource), {
        default: undefined,
        serverTiming,
        init: cache => xnotify.subscribe<ProjectId>(msg.projectSource.changed, async projectId => void cache.refresh([projectId], { dropCached: true })),
      }),
      getInstallationId: cachedCall(db.projectSource.getInstallationId.bind(db.projectSource), {
        default: undefined,
        serverTiming,
        init: cache => xnotify.subscribe<ProjectId>(msg.projectSource.changed, async projectId => void cache.refresh([projectId], { dropCached: true })),
      }),
    };
    this.queues = {
      createQueue: db.queues.createQueue.bind(db.queues),
      listJobs: cachedCall(db.queues.listJobs.bind(db.queues), {
        default: {
          queued: { jobs: [], count: 0 },
          active: { jobs: [], count: 0 },
          archived: { jobs: [], count: 0 },
        },
        serverTiming,
      }),
      listActiveJobs: cachedCall(db.queues.listActiveJobs.bind(db.queues), { serverTiming, default: { count: 0, jobs: [] }}),
      listQueuedJobs: cachedCall(db.queues.listQueuedJobs.bind(db.queues), { serverTiming, default: { count: 0, jobs: [] }}),
      listArchivedJobs: cachedCall(db.queues.listArchivedJobs.bind(db.queues), { serverTiming, default: { count: 0, jobs: [] }}),
    };
    this.productPlans = {
      create: async (plan: NewProductPlan) => {
        using timing = serverTiming.start('db');
        const productPlan = await db.productPlans.create(plan);
        productPlan && await xnotify.notify(msg.productPlans.created, productPlan);
        return productPlan;
      },
      update: async (planId: ProductPlanId, update: Partial<ProductPlanUpdate>) => {
        using timing = serverTiming.start('db');
        const productPlan = await db.productPlans.update(planId, update);
        productPlan && await xnotify.notify(msg.productPlans.updated, { planId, ...update });
        return productPlan;
      },
      delete: async (planId: ProductPlanId) => {
        using timing = serverTiming.start('db');
        const productPlan = await db.productPlans.delete(planId);
        productPlan && await xnotify.notify(msg.productPlans.deleted, productPlan);
        return productPlan;
      },
      get: cachedCall(db.productPlans.get.bind(db.productPlans), {
        serverTiming,
        default: undefined,
        init: cache => xnotify.subscribeAll<ProductPlan>([msg.productPlans.created, msg.productPlans.updated, msg.productPlans.deleted], 
          async plan => void cache.refresh([plan.plan_id], { dropCached: true })),
      }),
      getByPublicId: cachedCall(db.productPlans.getByPublicId.bind(db.productPlans), {
        serverTiming,
        default: undefined,
        init: cache => xnotify.subscribeAll<ProductPlan>([msg.productPlans.created, msg.productPlans.updated, msg.productPlans.deleted], 
          async plan => void cache.refresh([plan.plan_public_id], { dropCached: true })),
      }),
      list: cachedCall(db.productPlans.list.bind(db.productPlans), {
        serverTiming,
        default: [],
        init: cache => xnotify.subscribeAll<ProductPlan>([msg.productPlans.created, msg.productPlans.updated, msg.productPlans.deleted], 
          async () => void cache.clear()),
      }),
    };
  }

  async close() {
    return this._db.close();
  }

  /**
   * Returns inner database. Using this database directly skips ALL CACHING.
   */
  uncachedDatabase() { return this._db; }
}

export function toWireUser(user: User, isSuperUser: boolean): WireTypes.User {
  return  {
    isSuperUser: isSuperUser,
    userId: user.user_public_id,
    userLogin: user.user_login,
    userName: user.user_name,
    avatarUrl: user.user_avatar_url ?? undefined,
  };
}

export function toWireProjectRole(role: ProjectAccessRole): WireTypes.ProjectRole {
  switch (role) {
    case Database.projectAccessRole.editor: return 'editor';
    case Database.projectAccessRole.viewer: return 'viewer';
  }
  throw new Error(`unknown project role - ${role}`)
}

export function toWireOrgRole(principal: User|undefined, org: Organization, role: OrgAccessRole): WireTypes.OrgRole {
  if (principal && principal.user_id === org.owner_id)
    return 'owner';
  switch (role) {
    case Database.orgAccessRole.admin: return 'admin';
    case Database.orgAccessRole.member: return 'member';
  }
  throw new Error(`unknown org role - ${role}`)
}

export function fromWireProjectRole(role: 'editor'|'viewer'): ProjectAccessRole {
  if (role === 'editor')
    return Database.projectAccessRole.editor;
  if (role === 'viewer')
    return Database.projectAccessRole.viewer;
  throw new Error(`untranslatable wire role: ${role}`);
}


export function fromWireOrgRole(role: WireTypes.OrgRole): OrgAccessRole {
  if (role === 'admin')
    return Database.orgAccessRole.admin;
  if (role === 'member')
    return Database.orgAccessRole.member;
  throw new Error(`untranslatable org role: ${role}`);
}

function toWireAuthType(authType: SourceAuthType): string {
  if (authType === Database.sourceAuthType.githubPat)
    return 'github_pat';
  if (authType === Database.sourceAuthType.githubApp)
    return 'github_app';
  throw new Error(`unknown source type: ${authType}`);
}

function toWireVisibility(visibility: ProjectVisibility|null): WireTypes.ProjectVisibility {
  if (visibility === null || visibility === Database.projectVisibility.private)
    return 'private';
  if (visibility === Database.projectVisibility.public)
    return 'public';
  throw new Error(`unknown project visibility: ${visibility}`);
}

export function fromWireVisibility(visibility: string|undefined): ProjectVisibility {
  if (visibility === 'private')
    return Database.projectVisibility.private;
  if (visibility === 'public')
    return Database.projectVisibility.public;
  return Database.projectVisibility.private;
}

export function toWireOrg(principal: User|undefined, org: Organization, role: OrgAccessRole|undefined, limitations: WireTypes.BillingStatus|undefined): WireTypes.Organization {
  return {
    orgName: org.org_name,
    orgSlug: org.org_slug,
    access: role ? toWireOrgRole(principal, org, role) : undefined,
    restrictedCIUploads: limitations?.restrictedCIUploads ?? false,
    restrictedProjectAccess: limitations?.restrictedProjectAccess ?? false,
  };
}

export function toWireProject(org: WireTypes.Organization, project: Project, role: ProjectAccessRole): WireTypes.Project {
  return {
    sourceType: toWireAuthType(project.source_auth_type),
    projectPublicId: project.project_public_id,
    readWriteAccessToken: role === Database.projectAccessRole.editor ? project.flakiness_access_token : undefined,
    projectName: project.project_name,
    runsCount: project.reports_count,
    projectSlug: project.project_slug,
    visibility: toWireVisibility(project.visibility),
    sourceLastFetchTime: project.source_last_fetch_timestamp_seconds ? +project.source_last_fetch_timestamp_seconds * 1000 as FlakinessReport.UnixTimestampMS : undefined,
    sourceLastFetchHTTPCode: project.source_last_fetch_http_status ?? undefined,
    sourceOwner: project.source_owner_name,
    sourceRepo: project.source_repo_name,
    reportTimeIsUploadTime: project.report_time_is_upload_time ?? false,
    org,
    lastUploadTimestamp: project.last_upload_timestamp_seconds ? +project.last_upload_timestamp_seconds * 1000 as FlakinessReport.UnixTimestampMS : undefined,
    access: toWireProjectRole(role),
    preferredDataRetentionDays: project.preferred_data_retention_days ?? undefined,
  };
}

export function toWireJob(job: ActiveJob | QueuedJob | ArchivedJob): WireTypes.Job {
  return {
    queueName: job.queue_name,
    workerName: job.worker_name ?? undefined,
    heartbeatTimestampSeconds: job.execution_heartbeat_timestamp_seconds ?? undefined,
    id: job.id,
    jobId: job.job_id,
    data: job.data,
    concurrencyId: job.concurrency_id ?? undefined,
    category: job.category ?? undefined,
    executeAfterTimestampSeconds: job.execute_after_timestamp_seconds ?? undefined,
    retry: job.retry,
    submittedTimestampSeconds: job.submitted_timestamp_seconds ?? undefined,
    executionError: job.execution_error ?? undefined,
    executionResult: job.execution_result ?? undefined,
    executionDurationSeconds: job.execution_duration_seconds ?? undefined,
    executionTimestampSeconds: job.execution_timestamp_seconds ?? undefined,
  }
}

export function toWireUserSession(userSession: UserSession): WireTypes.UserSession {
  return {
    lastAccessTimestampSeconds: userSession.last_access_timestamp_seconds,
    sessionPublicId: userSession.session_public_id,
    name: userSession.session_name ?? undefined,
    clientId: userSession.client_id,
  };
}
