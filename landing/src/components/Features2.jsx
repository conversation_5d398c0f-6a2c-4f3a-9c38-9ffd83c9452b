import { motion } from "framer-motion";

import { CheckArrowIcon } from "../assets/icons/CheckArrowIcon";

import feature2 from "../assets/images/java.png";
import feature1 from "../assets/images/nodejs.png";
import feature3 from "../assets/images/python.png";

export const Features2 = () => (
  <section className="w-full bg-bgDark2 mt-12 sm:mt-24 mb-12 lg:my-20 lg:mb-24 pt-4">
    <motion.div
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay: 0.2 }}
    >
      <div className="flex flex-wrap items-center 2xl:w-[1450px] xl:w-[1300px] w-11/12 mx-auto md:pl-4 xl:pr-16 xl:pl-16">
        <div className="w-3/4 mx-auto lg:w-1/2 flex flex-wrap lg:-mx-4 sm:pr-8 lg:pt-10 justify-center lg:pl-4 xl:px-8 order-last lg:order-first">
          <div className="mb-8 lg:mb-0 w-full sm:w-1/2 px-2 lg:px-0">
            <div className="mb-4 py-3 pl-3 pr-2 rounded">
              <img
                src={feature1.src}
                alt="Feature image 1"
                className="rounded-xl  main-border-gray mx-auto sm:mx-unset"
                aria-label="Feature image 1"
              />
            </div>
            <div className="py-3 pl-3 pr-2 rounded ">
              <img
                src={feature2.src}
                alt="Feature image 2"
                className="rounded-xl  main-border-gray mx-auto sm:mx-unset"
                aria-label="Feature image 2"
              />
            </div>
          </div>
          <div className="w-1/2 lg:mt-20  pt-12 lg:pt-0 px-2 hidden sm:inline-block">
            <div className="mb-4 py-3 pl-3 pr-2 rounded-lg ">
              <img
                src={feature3.src}
                alt="Feature image 3"
                className="rounded-xl  main-border-gray"
                aria-label="Feature image 3"
              />
            </div>
          </div>
        </div>
        
        <div className="w-full lg:w-1/2 mb-12 lg:mb-0 xl:pl-8">
          <div className="mx-auto lg:mx-auto w-11/12 sm:w-4/5 md:w-3/4 lg:w-unset">
            <span className="block-subtitle">UNIFIED TESTING PLATFORM</span>
            <h2 className="mt-6 mb-8 text-4xl lg:text-5xl block-big-title">
              Single tool for all your tests
            </h2>
            <p className="mb-12 text-secondaryText leading-loose">
              Flakiness.io consolidates all your tests into one powerful dashboard,
              giving you complete visibility without the tool fatigue.
            </p>
            <ul className="mb-6 text-primaryText">
              <li className="mb-4 flex">
                <CheckArrowIcon />
                <div className="flex flex-col">
                  <strong>Node.js Ecosystem</strong>
                  <span className="text-secondaryText">Includes native Playwright Test reporter.</span>
                </div>
              </li>
              <li className="mb-4 flex">
                <CheckArrowIcon />
                <div className="flex flex-col">
                  <strong>Python 3+</strong>
                  <span className="text-secondaryText">Includes native Pytest reporter.</span>
                </div>
              </li>
              <li className="mb-4 flex">
                <CheckArrowIcon />
                <div className="flex flex-col">
                  <strong>Java</strong>
                  <span className="text-secondaryText">Includes native support for JUnit XML reports.</span>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </motion.div>
  </section>
);
