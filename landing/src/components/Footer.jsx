import { FlakinessLogo } from "../assets/logos/FlakinessLogo";

const footerData = [
  {
    title: "Legal",
    items: [
      { name: "Terms & Conditions", href: '/tos' },
      { name: "Privacy Policy", href: '/privacy' },
      { name: "Security", href: '/security' },
    ],
  },
];

export const Footer = () => {
  return (
    <footer aria-label="Site footer">
      <div className="pt-10  lg:pt-20 lg:pb-16 bg-bgDark1 radius-for-skewed ">
        <div className="container mx-auto px-4 w-4/5 md:w-11/12 lg:w-10/12 xl:w-4/5 2xl:w-2/3">
          <div className="flex flex-wrap">
            <div className="w-full lg:w-1/3 mb-16 lg:mb-0">
              <div className="flex justify-center lg:justify-start items-center grow basis-0">
                <div className="text-white mr-2 text-6xl">
                  <FlakinessLogo />
                </div>
                <div className="text-white font-['Inter'] font-bold text-xl">
                  Flakiness.io
                </div>
              </div>
              <div className="pt-6 text-secondaryText">
                <div>© 2024–2025 Degu Labs Inc</div>
                <div>Reach out <a href="mailto:<EMAIL>"><EMAIL></a></div>
              </div>
            </div>
            <div className="w-full lg:w-2/3  lg:pl-16 hidden lg:flex flex-wrap justify-around">
              {footerData.map(({ title, items}) => (
                <div className="w-full md:w-1/2 lg:w-auto mb-16 md:mb-0" key={title}>
                  <h3 className="mb-6 text-2xl font-bold text-primaryText">{title}</h3>
                  <ul>
                    {items.map((item, index) => (
                      <li key={`${item.name}-${index}`} className="mb-4">
                        <a
                          className="text-gray-400 hover:text-gray-300"
                          href={item.href}
                          aria-label={item}
                        >
                          {item.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
