{"name": "flakiness", "version": "0.133.0", "private": true, "scripts": {"minor": "./version.mjs minor", "patch": "./version.mjs patch", "dev": "npx kubik --env-file=.env.dev -w $(find . -name build.mts) ./app.mts ./stripe.mts", "dev+billing": "npx kubik --env-file=.env.dev+billing -w $(find . -name build.mts) ./app.mts ./stripe.mts", "prod": "npx kubik --env-file=.env.prodlocal -w ./server.mts ./web/build.mts ./experimental/build.mts ./landing/build.mts", "build": "npx kubik $(find . -name build.mts)", "perf": "node --max-old-space-size=10240 --enable-source-maps --env-file=.env.prodlocal experimental/lib/perf_filter.js"}, "engines": {"node": ">=24"}, "author": "Degu Labs, Inc", "license": "Fair Source 100", "workspaces": ["./report", "./sdk", "./docs", "./landing", "./devenv", "./database", "./server", "./shared", "./experimental", "./e2e", "./web"], "devDependencies": {"@playwright/test": "^1.54.0", "@types/node": "^22.10.2", "esbuild": "^0.27.0", "glob": "^10.3.10", "kubik": "^0.24.0", "tsx": "^4.19.2", "typescript": "^5.6.2"}}