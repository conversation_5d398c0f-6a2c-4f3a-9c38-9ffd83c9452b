#!/usr/bin/env npx kubik

import path from 'path';
import esbuild from 'esbuild';
import fs from 'fs';
import { Task } from 'kubik';
import { glob } from 'glob';

const { __dirname, $ } = Task.init(import.meta, {
  name: 'e2e',
  watch: [ './tests', './fixtures', 'playwright.config.ts', 'tsconfig.json' ],
  deps: [ '../database/build.mts', '../shared/build.mts', '../server/build.mts', ],
});

await $`tsc --pretty -p .`;
