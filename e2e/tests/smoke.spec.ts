import { expect } from '@playwright/test';
import assert from 'assert';
import { $ } from 'execa';
import fs from 'fs/promises';
import { e2e } from '../fixtures/fixtures.js';

e2e('should create new project and upload report', async ({ page, server, ghTestUser }) => {
  e2e.slow();
  console.log(e2e.info().timeout);
  await e2e.step('login as test user', async () => {
    await page.goto(server + '/login-test-user');
  });

  await e2e.step('create new organization', async () => {
    await page.getByRole('link', { name: 'New organization' }).click();
    await page.locator('pick-org-name input').fill('flakiness-for-test');
    await page.getByRole('button', { name: 'Create organization' }).click();
  });

  await page.getByRole('link', { name: 'flakiness-for-test' }).click();

  await e2e.step('create new project', async () => {
    await page.getByRole('link', { name: 'New Project' }).click();
    await page.locator('input[name="project_name"]').fill('demo-playwright-test');
    // Switch to "PAT" panel
    await page.locator('sl-tab[panel=pat]').click();
    await page.locator('source-github-pat input[name="source_url"]').fill('https://github.com/degulabs/demo-playwright-test');
    await page.locator('source-github-pat input[name="access_token"]').fill(ghTestUser.githubPat);
    await page.getByRole('button', { name: 'Create project' }).click();
  });

  const flakinessAccessToken = await e2e.step('get flakiness access token', async () => {
    await page.getByRole('link', { name: 'demo-playwright-test' }).click();
    await expect(page.getByTestId('flakiness-access-token').first()).not.toBeEmpty();
    const flakinessAccessToken = await page.getByTestId('flakiness-access-token').first().textContent();
    assert(flakinessAccessToken !== null);
    return flakinessAccessToken;
  });
  
  const repoPath = await e2e.step('clone demo repository', async () => {  
    const outputDir = e2e.info().outputDir;
    const repoPath = e2e.info().outputPath('demo-playwright-test');
    await fs.mkdir(outputDir, { recursive: true }).catch(e => {});
    await $({ cwd: outputDir })`git clone https://github.com/flakiness/demo-playwright-test`;
    await $({ cwd: repoPath })`npm install`;
    
    return repoPath;
  });

  await e2e.step('upload report to test server', async () => {
    await $({
      cwd: repoPath,
      env: {
        FLAKINESS_ACCESS_TOKEN: flakinessAccessToken,
        FLAKINESS_ENDPOINT: server,
      }
    })`npx playwright test`.catch(e => { /* tests fail - so catch error */});
  });

  await e2e.step('wait for report to be processed', async () => {
    await expect(async () => {
      await page.reload();
      await expect(page.getByTestId('plane-tests')).toContainText('3');
      await expect(page.getByTestId('plane-envs')).toContainText('1');
    }).toPass();  
  });

  await e2e.step('upload another report to test server', async () => {
    await $({
      cwd: repoPath,
      env: {
        FLAKINESS_ACCESS_TOKEN: flakinessAccessToken,
        FLAKINESS_ENDPOINT: server,
        FK_ENV_CRONJOB: '1',
        DEBUG: 'flakiness:*',
      }
    })`npx playwright test`.catch(e => { /* tests fail - so catch error */});
  });

  await e2e.step('wait for second report to be processed', async () => {
    await expect(async () => {
      await page.reload();
      await expect(page.getByTestId('plane-tests')).toContainText('3');
      await expect(page.getByTestId('plane-envs')).toContainText('2');
    }).toPass();  
  });
});
