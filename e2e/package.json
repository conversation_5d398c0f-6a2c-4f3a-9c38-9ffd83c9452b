{"name": "@flakiness/e2e-tests", "private": true, "scripts": {"test": " source ../.env.dev && npx playwright test"}, "version": "0.133.0", "description": "", "type": "module", "types": "./types/src", "main": "./lib", "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@flakiness/database": "0.133.0", "@flakiness/server": "0.133.0", "@flakiness/shared": "0.133.0", "@playwright/test": "^1.54.0", "dotenv": "^16.4.5", "execa": "^9.4.0", "podkeeper": "^0.3.1"}}