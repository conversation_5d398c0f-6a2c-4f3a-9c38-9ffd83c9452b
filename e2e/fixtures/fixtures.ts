import { Database, DatabaseConfig } from '@flakiness/database';
import { HTTPServer } from '@flakiness/server/node/httpServer.js';
import { GithubApp } from '@flakiness/server/node/integrations/githubApp.js';
import { License } from '@flakiness/server/node/license.js';
import { S3, S3Bucket } from '@flakiness/server/node/s3.js';
import { Services } from '@flakiness/server/node/services.js';
import { test as baseTest } from '@playwright/test';
import path from 'path';
import { Minio, Postgres } from 'podkeeper';
import url from 'url';

const __filename = url.fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const S3_BUCKET_NAME = 'flakiness-data-2';

type GithubTestUser = {
  login: string,
  email: string,
  password: string,
  githubPat: string,
}

const testUser = {
  login: process.env.GITHUB_TEST_USER_LOGIN!,
  name: process.env.GITHUB_TEST_USER_NAME!,
  email: process.env.GITHUB_TEST_USER_EMAIL!,
  password: process.env.GITHUB_TEST_USER_PASSWORD!,
  githubPat: process.env.GITHUB_TEST_USER_PAT!,
  githubId: parseInt(process.env.GITHUB_TEST_USER_GITHUB_ID!, 10),
  avatarUrl: process.env.GITHUB_TEST_USER_AVATAR_URL!,
};

export const e2e = baseTest.extend<{ s3data: S3Bucket, minio: Minio, pg: Postgres, server: string, ghTestUser: GithubTestUser }>({
  minio: async ({ }, use) => {
    const minio = await Minio.start();
    await use(minio);
    await minio.stop();
  },

  s3data: async ({ minio }, use) => {
    const client = new S3({
      accessKeyId: minio.accessKeyId(),
      endpoint: minio.apiEndpoint(),
      region: 'wnam',
      secretAccessKey: minio.secretAccessKey(),
    });
    const bucket = await client.createBucket(S3_BUCKET_NAME);
    await use(bucket);
  },

  pg: async ({ }, use) => {
    const pg = await Postgres.start();
    const dbConfig: DatabaseConfig = {
      ...pg.connectOptions(),
      encryptionKey: Database.createEncryptionKeyForTest(),
    };
    const { error } = await Database.migrate(dbConfig, async migrator => migrator.migrateToLatest());
    if (error)
      throw error;
    await use(pg);
    await pg.stop();
  },

  server: async ({ s3data, pg, minio }, use) => {
    const services = await Services.initializeOrDie({
      dbConfig: {
        ...pg.connectOptions(),
        encryptionKey: Database.createEncryptionKeyForTest(),
      },
      githubAppConfig: await GithubApp.configFromEnvOrDie(),
      licenseKey: await License.licenseKeyFromEnvOrDie(),
      s3config: {
        endpoint: minio.apiEndpoint(),
        accessKeyId: minio.accessKeyId(),
        secretAccessKey: minio.secretAccessKey(),
        region: 'wnam',
        bucketName: S3_BUCKET_NAME,
      },
    });

    const server = await HTTPServer.create(services, {
      frontendPath: path.join(__dirname, '../../web/dist'),
      jwtSignSecret: 'Testing secret to sign stuff',
      port: 0,
      testUser,
      onlySuperusersCanCreateOrganizations: false,
    });
    await services.startBackgroundProcessing('e2e tests');
    await use(server.address());
    await services.stopBackgroundProcessing();
    await server.dispose();
    await services.dispose();
  },

  ghTestUser: async ({  }, use) => {
    // Github Test User
    await use({
      login: 'DeguLabs-Test-Account',
      email: '<EMAIL>',
      password: '3LeXJ2iHLvBQVUH',
      githubPat:  '****************************************',
    });
  }
});
