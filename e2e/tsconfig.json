{"include": ["tests/", "fixtures/"], "exclude": ["lib"], "compilerOptions": {"allowSyntheticDefaultImports": true, "noEmit": true, "strict": true, "target": "ESNext", "isolatedModules": true, "resolveJsonModule": true, "moduleResolution": "NodeNext", "esModuleInterop": true, "module": "NodeNext", "lib": ["esnext", "DOM", "DOM.Iterable"], "incremental": true, "tsBuildInfoFile": "./.tsconfig.tsbuildinfo", "types": ["node"]}, "references": [{"path": "../server"}, {"path": "../database"}, {"path": "../shared"}]}