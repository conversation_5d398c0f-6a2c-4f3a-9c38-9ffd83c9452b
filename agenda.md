<PERSON><PERSON>'s feedback on DOCUMENTATION:

- JUnit XML: integration is very unclear.
    * explicitly, write `--output-dir` in the `convert-junit`.
- "Usecases" in the overview section gives bias towards end-2-end tests:
  "playwright end-2-end", "..."
    * idea: use AI to check for biases?
- in "Flakiness Report" doc: s/Generation/Report Generation/g
- Comments regarding demo:
    * everything is red-and-yellow. Not convincing!
    * commits are not clickable due to 404
- In the "test statuses", the icon for "skipped" is smaller
- The extension of statuses to the multitude of tests is confusing:
  once there's a single test flakiness, the whole group of tests is considered
  flaky. Maybe have a percentage of flakiness?
- Flakiness Score for test: how flaky is the test? This will help to prioritize
  engineering effort: "What would be more impactful?"
- FQL:
    - missing that "AND" is implied
    - is "OR" missing?
    - escaping rules are unclear
- In Test Status documentation, icons are different from what we have in actual
  product

<PERSON><PERSON>'s feedback on PRODUCT:
- The word "timeline" is unclear! Wtf is this? "Environment" would be much more clear.
  Can we drop "timeline" altogether?
- On a quick glance on the dashboard, it's unclear if the status is GOOD or BAD
  We should have some big colorcoding maybe?
- On Timelines tab in the report, please show status!
- Status of the test should PROMINENTLY SHOW that it leads to the last run!
    * The click on the test should lead to the last test run
    * Click on the chart should lead to test history
- Browser Navigation: in-page navigation should not add to browser history, instead,
  it should overwrite last entry?
- The test-wise history is confusing: these are durations?! Artem was sure
  these are Test Runs count per day. He didn't even know that these are the
  "status & duration of the last test run in that day".
- Idea: project should have a timezone in settings so that all people see the
  same dashboard.
- Test History: overall, not so usable:
    * unclear that commits minimap is clickable
    * the long list of commits is painful to scroll
- Calendar:
    * Let's do a normal calendar. Dude. Don't be fancy. This one is hard - hard
      to pinpoint exact date.
    * By default, calendar is always ORANGE if there's at least one flaky test. Dude!
      Make it configurable: if only one test is flaky, then it should be very much green!
- Runs page:
    * Show run's status
    * Show stats
    * Show commit and commit's branch
    * Header with stats
    * 4 run statuses:
- In test history, we should bring focus to regressions. Maybe the whole
  commit with regression should have highlight?



TODO:
- the execution-calendar should be a regular calendar, maybe?
- commits history should show a graph of commits; commits can
  be selected, and then the selected commit + 20 more commits
  are displayed. There's a button "next 20 commits". By default,
  the last commit is selected. The selected commit is highlighted
  on the chart.
- the commit-history view should be nicer.
  * the selected commit is HIGHLIGHTED in the chart
  * the history is displayed starting from the commit
- address a bunch of feedback
- the initial markdown for empty project should show `npx flakiness link`
    * make sure `npx flakiness link` accepts url, like this:
      npx flakiness link https://flakiness.io/flakiness/flakiness
- make sure report endpoints are separate from database endpoints.
- deploy custom database-only servers?


Renal feedback:
1. вроде там обычно пишут не цену за год а типа:
   $75/month
   $68.5/month - (это годовая в пересчете на месяц)
2. коммиты не открываются, это наверное ожидаемо (но может показаться что
   что-то сломано)
3. А как заказывать? тут только Get Started кнопка и она ведет в доки
4. Какое-нибудь короткое видеодемо мб тоже прикол
5. А пермишены нельзя ослабить? а то Act on your behalf звучит страшно

Max feedback:
2. https://flakiness.io/flakiness/kotlin is extremly slow on first visit!
9. OIDC should not require you to set a token anymore, should be easy to implement imo.

Action points:
3. "Act on Your behalf" is scary. Any way around it?

Egor Feedback:
1. Выглядит хорошо, без лишних деталей, классно что есть демка покликать.
2. Не понял есть это или нет, но хочется еще сортировать тесты так, чтобы те,
   которые изменили статус после каммита были сверху (либо поломались, либо
   стали долгими)
3. Может не хватает мини туториалов, а может гайда по инсталяции и хватит.
4. Нет ли пробного периода без суппорта? Надо сразу на месяц покупать?

TODO:
- delete execution-history component?

- Orchestration?
- on flakiness.io, I'd like to have report.flakiness.io for local report. Is this possible?
- on the landing page, show the "Reports processed" or "test runs processed"
  counter. We do have many test runs coming from kotlin + flakiness.
- show trend on # of test runs - this can explain why total time on timelines grows.
- speedup branch filtering (takes too long for Kotlin)
- branches page
- commit report
- page-run: show "other runs" in the commit
- archive report?
- For local report, pick history somehow?
- add validation in the report that all test names inside a file are different. Or should I?
- device auth
    * cache device auth table?
* do NOT expose `bench` in mitata reporter; make sure all
  test names are UNIQUE.
* use B.run() in mitata reporter instead of mitata.run().
  This way we'll be able to accurately match runs with trials.
* timelines editor should be nicer
* how to protect from too many timelines? This might slow down
  EVERYTHING.
* Should queue poll less often, but rely on xNotify to awake workers?
* commitStats MUST NOT send all runStats! Must be paged instead?
* index re-build: can i rebuild backwards? I want latest reports first, and then history
* test index: shall I include test suites? The test -> testSuite relation
  is not trivial
* report results: pie chart with results
- Issue: The same user can start unlimited number of Free Trials.
  * In stripe, we can set metadata for "customer" objects. Can we do this to
    avoid the second free trial for the same customer?
- hard limits for data uploads have to be configured?
- Polyrepo support!
- configure regression look-back in project settings ??

=== Observability ===

Right now, we observe deployment manually :) However, we should:
- monitor average time a job is queued / is executed
- setup prometheus alerts when some metrics go off (i.e. queued time is becoming
  more than 1 hour)

=== Server ===

DDoS'ing the server is easy now. We should:
* limit requests per person
* only single computation request per user should be computed at the same
  time

=== Enhancements ===

IDEA:
- ranges could be stored efficiently if i offset the numbers with a prefix!!! (Does VLQ already do this?)
- in Git.ts, we can VLQ-compress strands. Test how much it saves.

---

Usecases:
- local report
- shards: automatically merged
- cron jobs: report shows last results
- test history

TODO:
- trends page
- plan selection should be done at the point of org creation? maybe? Otherwise it's
  pretty aggressive.
- show test error (if any) when hovering over test status

Q:
- the ensure-all-reports-are-in-index job might conflict with data retention;
  we should not schedule reports to addition if they don't satisfy data retention policy.

Flakiness.io: Trend & Regression analytics for tests.

- Rich HTML Reports (feature-parity with Playwright HTML report)
- Trends & Regressions
- Flexible Programmatic Environment configuration
- Local Report

Flakiness.io features:

=== Target 1: SaaS Beta Release

- Support OIDC connect for github actions / gitlab actions (as max recommended)
  * https://docs.github.com/en/actions/security-for-github-actions/security-hardening-your-deployments/about-security-hardening-with-openid-connect

- When app installation gets removed, we should show a proper notification
  * right now, it is "something went wrong"

- Deployment script for cr.flakiness.io
  * otherwise we will forget how to do deploy it.

