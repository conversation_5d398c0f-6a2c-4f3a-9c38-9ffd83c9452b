#! /usr/bin/env node

import { FlakinessReport } from '@flakiness/report';
import { ReportUploader } from '@flakiness/sdk/uploader';
import { Services } from '@flakiness/server/node/services.js';
import { measure } from '@flakiness/shared/common/utils.js';
import assert from 'assert';
import { Kysely, PostgresDialect } from 'kysely';
import ms from 'ms';
import pg from 'pg';
import { EnhancedJSONReport, parseMicrosoftPlaywrightReport } from './utils.js';

// Database connection configuration
const backofficeDB = new Kysely<any>({
  dialect: new PostgresDialect({
    pool: new pg.Pool({
      host: process.env.BACKOFFICE_PGHOST,
      port: parseInt(process.env.BACKOFFICE_PGPORT || '5432', 10),
      database: process.env.BACKOFFICE_PGDATABASE,
      user: process.env.BACKOFFICE_PGUSER,
      password: process.env.BACKOFFICE_PGPASSWORD,
    }),
  }),
});

// Ensure table exists with primary key
await backofficeDB.schema
  .createTable('uploaded_report_ids')
  .ifNotExists()
  .addColumn('report_id', 'varchar(36)', col => 
    col.notNull().primaryKey()
  )
  .addColumn('added_timestamp_ms', 'bigint', col => 
    col.notNull()
  )
  .execute();

// Drop all reports that were uploaded more than 1 year ago.
await backofficeDB
  .deleteFrom('uploaded_report_ids')
  .where('added_timestamp_ms', '<', Date.now() - ms('365 days'))
  .execute();

// Fetch all report IDs from the last 30 days
const uploadedReportIds = new Set((await backofficeDB
  .selectFrom('uploaded_report_ids')
  .select('report_id')
  .where('added_timestamp_ms', '>', Date.now() - ms('30 days'))
  .execute()).map(x => x.report_id));

const config = await Services.configFromEnvOrDie();
const services = await Services.initializeOrDie(config);

const org = await services.db.orgs.getBySlug('flakiness');
const project = org ? await services.db.projects.getBySlug(org.org_id, 'playwright') : undefined;
if (!project)
  throw new Error('failed to find flakiness/playwright project!');

const repo = await services.gitWorker.getRepo(project.project_public_id);
assert(repo);

// Fetch latest commits; give our selve 1 minute only to do the job.
const provider = await services.gitWorker.getProvider(project);
await repo.fetch(provider, [], AbortSignal.timeout(ms('1 minutes'))).catch(e => void e);
const commits = repo.iterator(repo.defaultBranch().commit.commitId).collect({
  sinceTimestamp: (Date.now() - ms('7 days')) as FlakinessReport.UnixTimestampMS,
});

await services.dispose();

const uploader = new ReportUploader({
  flakinessAccessToken: project.flakiness_access_token,
  flakinessEndpoint: process.env.GITHUB_APP_CALLBACK_URL!,
});

for (const commit of commits) {
  const commitId = commit.commitId;
  try {
    const m = measure();
    console.log(`Start processing commit: ${commitId.substring(0, 8)}`);
    const response = await fetch(blobURL(commitId), { headers: {'Accept-Encoding': 'identity'}});
    if (!response.ok || !response.body) {
      console.log(`Failed to download commit ${commitId} - ${response.status} ${response.statusText}`);
      continue;
    }
    const text = await response.text();
    m(`- downloaded`);
    // Parse all reports, and filter out those that were already uploaded.
    const pwReports = (JSON.parse(text) as EnhancedJSONReport[])
      .filter(pwReport => !uploadedReportIds.has(pwReport.metadata.uuid));
    let totalParseTime = 0;
    let totalUploadTime = 0;
    for (const pwReport of pwReports) {
      const mParse = measure();
      const report = await parseMicrosoftPlaywrightReport(pwReport);
      totalParseTime += mParse.duration();
      const mUpload = measure();
      const result = await uploader.createUpload(report, []).upload({ syncCompression: true });
      if (!result.success)
        console.error(`- upload failed: ${result.message ?? '<unknown>'}`);
      else {
        await backofficeDB
          .insertInto('uploaded_report_ids')
          .values([{
            report_id: pwReport.metadata.uuid,
            added_timestamp_ms: Date.now(),
          }])
          .execute();
      }
      totalUploadTime = mUpload.duration();
    }
    m(`- uploaded ${pwReports.length} reports; avg. parse time: ${Math.round(totalParseTime / pwReports.length)}ms, avg. upload time: ${Math.round(totalUploadTime / pwReports.length)} `);
  } catch (e: any) {
    console.log('Error when processing ' + commitId + ' ' + e.message);
    console.log(e.stack);
  }
}
await backofficeDB.destroy();

function blobURL(sha: string) {
  return `https://folioflakinessdashboard.blob.core.windows.net/dashboards/raw/${sha}.json`;
}
