import { FlakinessReport } from '@flakiness/report';
import { ReportUploader } from '@flakiness/sdk/uploader';
import { computeGitRoot, createEnvironment, getCallerLocation, gitCommitInfo, gitFilePath, NormalizedPath, normalizePath } from '@flakiness/sdk/utils';
import { Multimap } from '@flakiness/shared/common/multimap.js';
import * as mitata from 'mitata';
import path from 'path';
import { V8CPUProfiler } from './v8CpuProfiler.js';

export function humanNumber(x: number) {
  if (x < 1000)
    return x;
  x /= 1000;
  if (x < 1000)
    return x.toFixed(0) + 'K';
  x /= 1000;
  return x.toFixed(1) + 'M';
}

export function ratio2percent(ratio: number) {
  return (ratio * 100|0) + '%';
}

type Test = {
  name: string,
  location?: FlakinessReport.Location,
}

class Group {
  tests: Test[] = [];
  groups: Group[] = [];
  constructor(
    public name: string,
    public type: FlakinessReport.SuiteType,
    public location?: FlakinessReport.Location,
  ) {

  }
}

type Context = {
  cumulativeTime: number,
  trials: mitata.trial[],
  flatIndex: number,
  grep?: RegExp,
}

function toFKSuite(ctx: Context, group: Group): FlakinessReport.Suite {
  return {
    location: group.location,
    title: group.name,
    type: group.type,
    suites: group.groups.map(group => toFKSuite(ctx, group)),
    tests: group.tests.map(test => toFKTests(ctx, test)).flat(),
  }
}

function toFKTests(ctx: Context, test: Test): FlakinessReport.Test[] {
  if (ctx.grep && !ctx.grep.test(test.name))
    return [];
  const uniqueNames = new Multimap<string, mitata.Run>();
  return ctx.trials[ctx.flatIndex++].runs.map(run => {
    const startTimestamp = ctx.cumulativeTime;
    // Mitata stats are in picoseconds.
    ctx.cumulativeTime += Math.round((run.stats?.samples ?? []).reduce((acc, x) => acc + x, 0) / 1000000);
    let duration = run.stats ? run.stats.avg / 1000 : 0;
    // If duration is less then 1ms, then round to nanoseconds.
    // Otherwise, round to ms.
    if (duration > 0 && duration < 1000)
      duration = Math.round(duration) / 1000;
    else
      duration = Math.round(duration / 1000);
    let title = run.name;
    if (uniqueNames.hasAny(title)) {
      uniqueNames.set(title, run);
      title += `(${uniqueNames.getAll(title).length})`;
    }
    return {
      title: run.name,
      location: test.location,
      attempts: [{
        timeout: 0 as FlakinessReport.DurationMS,
        annotations: [],
        environmentIdx: 0,
        expectedStatus: 'passed',
        status: run.stats ? 'passed' : 'failed',
        startTimestamp: startTimestamp as FlakinessReport.UnixTimestampMS,
        duration: duration as FlakinessReport.DurationMS,
        parallelIndex: 0,
        errors: run.error && run.error instanceof Error ? [{
          message: run.error.message,
          stack: run.error.stack,
        }] : run.error ? [{
          value: JSON.stringify(run.error),
        }] : undefined,
      }],
    };
  });
}

export class MitataReporter {

  private _startTimestamp = Date.now();

  private _root: Group;

  private _currentGroup: Group;
  private _configPath: FlakinessReport.GitFilePath;
  private _gitRoot: NormalizedPath;
  private _envOptions?: Record<string, string>;
  private _name: string;

  constructor(options: {
    name: string,
    configPath: string,
    envOptions?: Record<string, string>
  }) {
    this._name = options.name;
    this._envOptions = options.envOptions;
    this._gitRoot = computeGitRoot(path.dirname(options.configPath));
    this._configPath = gitFilePath(this._gitRoot, normalizePath(options.configPath));
    this._root = new Group(path.basename(this._configPath), 'file', {
      file: this._configPath,
      column: 1 as FlakinessReport.Number1Based,
      line: 1 as FlakinessReport.Number1Based,
    });
    this._currentGroup = this._root;
  }

  group(name: string, mitataSuffix: string, f: () => void) {
    const group = new Group(name, 'suite', getCallerLocation(this._gitRoot, 1));
    this._currentGroup.groups.push(group);
    const oldCurrent = this._currentGroup;
    this._currentGroup = group;
    mitata.group(mitataSuffix ? `${name}: ${mitataSuffix}` : name, f);
    this._currentGroup = oldCurrent;
  }

  bench(name: string, fn: () => any): mitata.B;
  bench(name: string, gen: (state: mitata.k_state) => mitata.Gen): mitata.B;
  bench(name: string, iter: (state: mitata.k_iter & mitata.k_state) => any): mitata.B;
  bench(name: string, callback: any): mitata.B {
    const test: Test = {
      name,
      location: getCallerLocation(this._gitRoot, 1),
    }
    this._currentGroup.tests.push(test);
    return mitata.bench(name, callback);
  }

  do_not_optimize(a: any) {
    mitata.do_not_optimize(a);
  }

  async cli(argv: string[]) {
    if (argv.includes(`--help`)) {
      console.log(`Use --grep, -g, --profile, -p`);
      return;
    }
    const grepIndex = argv.findIndex(a => a === '--grep' || a === '-g');
    let grep: RegExp|undefined;
    if (grepIndex !== -1) {
      if (argv[grepIndex + 1] === undefined) {
        console.error('Expected grep value after --grep flag');
        return;
      }
      grep = new RegExp(argv[grepIndex + 1]);
    }
    const v8profile = argv.includes('--profile') || argv.includes('-p');
    return await this.run({ grep, v8profile });
  }

  async run(options: {
    grep?: RegExp,
    v8profile?: boolean,
  }) {
    const v8profiler = options.v8profile ? new V8CPUProfiler() : undefined;
    await v8profiler?.start();
    const result = await mitata.run({
      filter: options.grep,
    });
    await v8profiler?.stop();

    // Create FlakinessReport from mitata results
    const ctx: Context = {
      cumulativeTime: this._startTimestamp,
      trials: result.benchmarks,
      flatIndex: 0,
      grep: options.grep,
    }
    const report: FlakinessReport.Report = {
      category: 'perf',
      commitId: gitCommitInfo(this._gitRoot),
      configPath: this._configPath,
      environments: [createEnvironment({
        name: this._name,
        userSuppliedData: this._envOptions,
      })],
      suites: [toFKSuite(ctx, this._root)],
      unattributedErrors: [],
      startTimestamp: this._startTimestamp as FlakinessReport.UnixTimestampMS,
      duration: Date.now() - this._startTimestamp as FlakinessReport.DurationMS,
    };

    await ReportUploader.upload({
      report,
      attachments: [],
      log: console.log,
    }).then(uploadError => {
      if (uploadError)
        process.exitCode = 1;
    });
  }
}
