#! /usr/bin/env node

import { FlakinessReport } from '@flakiness/report';
import { ReportUploader } from '@flakiness/sdk/uploader';
import { Services } from '@flakiness/server/node/services.js';
import { xxHashObject } from '@flakiness/shared/common/utils.js';
import assert from 'assert';
import { Kysely, PostgresDialect } from 'kysely';
import ms from 'ms';
import pg from 'pg';

// Database connection configuration
const backofficeDB = new Kysely<any>({
  dialect: new PostgresDialect({
    pool: new pg.Pool({
      host: process.env.BACKOFFICE_PGHOST,
      port: parseInt(process.env.BACKOFFICE_PGPORT || '5432', 10),
      database: process.env.BACKOFFICE_PGDATABASE,
      user: process.env.BACKOFFICE_PGUSER,
      password: process.env.BACKOFFICE_PGPASSWORD,
    }),
  }),
});

const TABLE_NAME = 'wk_uploaded_report_ids';

// Ensure table exists with primary key
await backofficeDB.schema
  .createTable(TABLE_NAME)
  .ifNotExists()
  .addColumn('report_id', 'varchar(36)', col => 
    col.notNull().primaryKey()
  )
  .addColumn('added_timestamp_ms', 'bigint', col => 
    col.notNull()
  )
  .execute();

// Drop all reports that were uploaded more than 1 year ago.
await backofficeDB
  .deleteFrom(TABLE_NAME)
  .where('added_timestamp_ms', '<', Date.now() - ms('365 days'))
  .execute();

// Fetch all report IDs from the last 30 days
const uploadedReportIds = new Set((await backofficeDB
  .selectFrom(TABLE_NAME)
  .select('report_id')
  .where('added_timestamp_ms', '>', Date.now() - ms('30 days'))
  .execute()).map(x => x.report_id));

const config = await Services.configFromEnvOrDie();
const services = await Services.initializeOrDie(config);


const org = await services.db.orgs.getBySlug('flakiness');
const project = org ? await services.db.projects.getBySlug(org.org_id, 'webkit') : undefined;
if (!project)
  throw new Error('failed to find flakiness/webkit project!');

const repo = await services.gitWorker.getRepo(project.project_public_id);
assert(repo);

// Fetch latest commits for the main branch.
const provider = await services.gitWorker.getProvider(project);
await repo.fetch(provider, [], AbortSignal.timeout(ms('2 minutes'))).catch(e => void e);
const commits = repo.iterator(repo.defaultBranch().commit.commitId).collect({
  sinceTimestamp: (Date.now() - ms('7 days')) as FlakinessReport.UnixTimestampMS,
});
await services.dispose();

const uploader = new ReportUploader({
  flakinessAccessToken: project.flakiness_access_token,
  flakinessEndpoint: process.env.GITHUB_APP_CALLBACK_URL!,
});

type WebkitCommitResult = {
  commits: {
    author: {
      emails: string[],
      name: string,
    },
    branch: string,
    hash: string,
    identifier: string,
    message: string,
    order: number,
    repository_id: string,
    timestamp: number,
  }[],
  configuration: Record<string, string|boolean|number>,
  suite: string,
  test_results: {
    details: {
      'build-number': string,
      'buildbot-master': string,
      'buildbot-worker': string,
      'builder-name': string,
    },
    run_stats: {
      end_time: number,
      start_time: number,
      tests_skipped: number,
    },
    results: any, // a.b.c.d.e.WebkitTestResult
  }
}[];

async function downloadWebkitCommitResults(commitId: string): Promise<WebkitCommitResult> {
  const response = await fetch(`https://results.webkit.org/api/upload?ref=${commitId}`);
  if (!response.ok)
    throw new Error(`HTTP error! Status: ${response.status}`);
  return await response.json();
}

for (const commit of commits) {
  const commitId = commit.commitId;
  console.log(`Start processing commit: ${commitId.substring(0, 8)}`);
  try {
    const json = await downloadWebkitCommitResults(commitId);
    for (const commit of json) {
      const runId = xxHashObject({
        salt: 'webkit2',
        details: commit.test_results.details,
        run_stats: commit.test_results.run_stats,
        configuration: commit.configuration,
      });
      if (uploadedReportIds.has(runId))
        continue;

      const rootSuite = iterateTests(commit.test_results.results);
      const report: FlakinessReport.Report = {
        category: 'webkit',
        commitId,
        startTimestamp: commit.test_results.run_stats.start_time * 1000 as FlakinessReport.UnixTimestampMS,
        duration: (commit.test_results.run_stats.end_time - commit.test_results.run_stats.start_time) * 1000 as FlakinessReport.DurationMS,
        url: await getBuildUrl({
          masterUrl: commit.test_results.details['buildbot-master'],
          builderName: commit.test_results.details['builder-name'],
          buildNumber: commit.test_results.details['build-number'],
        }),
        environments: [{
          name: commit.suite,
          userSuppliedData: commit.configuration,
        }],
        suites: rootSuite.suites ?? [],
        tests: rootSuite.tests ?? [],
        unattributedErrors: [],
      };

      const result = await uploader.createUpload(report, []).upload({ syncCompression: true });
      if (!result.success) {
        console.error(`- upload failed: ${result.message ?? '<unknown>'}`);
      } else {
        await backofficeDB
          .insertInto(TABLE_NAME)
          .values([{
            report_id: runId,
            added_timestamp_ms: Date.now(),
          }])
          .execute();
        console.log(`upload successful`);
      }
    }
  } catch (e: any) {
    console.log('Error when processing ' + commitId + ' ' + e.message);
    console.log(e.stack);
  }
}
await backofficeDB.destroy();

async function getBuildUrl(options: {
  masterUrl: string,
  builderName: string,
  buildNumber: string,
}) {
  // Buildbot V2 API endpoint
  const apiEndpoint = new URL(`https://${options.masterUrl}/api/v2/builders`);
  apiEndpoint.searchParams.append("name", options.builderName);
  apiEndpoint.searchParams.append("limit", "1"); // We only expect one builder with this exact name

  const response = await fetch(apiEndpoint.toString());
  if (!response.ok) {
    throw new Error(`HTTP error! Status: ${response.status}`);
  }

  const data = await response.json();
  if (!data.builders || data.builders.length === 0) {
    console.error(`Error: No builder found with name '${options.builderName}'`);
    return undefined;
  }

  // 2. Extract the builderid
  // The API returns a list of builders. We take the first one.
  const builderId = data.builders[0].builderid;
  // 3. Construct the final URL
  // Standard format: https://build.webkit.org/#/builders/<builder_id>/builds/<build_number>
  return `https://${options.masterUrl}/#/builders/${builderId}/builds/${options.buildNumber}`;
}


type WebkitStatus = 'PASS'|'FAIL'|'TIMEOUT'|'TEXT'|'IMAGE'|'CRASH';

function iterateTests(object: any, suite: FlakinessReport.Suite = {
  title: 'root',
  type: 'file',
  suites: [],
  tests: [],
}): FlakinessReport.Suite {
  for (const [title, obj] of Object.entries(object) as [any, any][]) {
    const valueKeys = new Set(Object.keys(obj));
    const isTest = !valueKeys.size || (typeof obj.time === 'number') || (typeof obj.actual === 'string') || (typeof obj.expected === 'string');
    if (isTest) {
      const actual: WebkitStatus = obj.actual ?? 'PASS';
      const expected: WebkitStatus[] = obj.expected ? obj.expected.split(' ') : ['PASS'];
      const duration = (obj.time ?? 0) as FlakinessReport.DurationMS;
      const status: FlakinessReport.TestStatus = actual === 'PASS' ? 'passed' : 
        actual === 'FAIL' || actual === 'TEXT' || actual === 'IMAGE' || actual === 'CRASH' ? 'failed' :
        'timedOut';
      const expectedStatus: FlakinessReport.TestStatus = expected.includes(actual) ? status : 
        status === 'passed' ? 'failed' : 'passed';

      const t: FlakinessReport.Test = {
        title,
        attempts: [{
          environmentIdx: 0,
          expectedStatus,
          status,
          startTimestamp: 0 as FlakinessReport.UnixTimestampMS,
          duration,
        }],
      };
      suite.tests!.push(t);
    } else {
      const s: FlakinessReport.Suite = {
        title,
        type: 'suite',
        suites: [],
        tests: [],
      };
      suite.suites!.push(s);
      iterateTests(obj, s);
    }
  }
  return suite;
}