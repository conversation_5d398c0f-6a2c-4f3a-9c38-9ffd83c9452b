import { FlakinessReport } from '@flakiness/report';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export type GitCommit = {
  commitId: FlakinessReport.CommitId,
  timestamp: FlakinessReport.UnixTimestampMS, // ms
  message: string,
  avatar_url?: string,
  author?: string,
  walkIndex: number,
  parents: FlakinessReport.CommitId[],
}

export async function listLocalCommits(gitRoot: string, head: string, count: number): Promise<GitCommit[]> {
  // Define separators for robust parsing of the git log output
  const FIELD_SEPARATOR = '|~|';
  const RECORD_SEPARATOR = '\0'; // Use the null character as a safe record separator

  // Define the custom format for the git log output.
  // This specifies which pieces of information we want for each commit.
  const prettyFormat = [
    '%H',  // %H:  Full commit hash
    '%at', // %at: Author date as a Unix timestamp (seconds since epoch)
    '%an', // %an: Author name
    '%s',  // %s:  Subject (the first line of the commit message)
    '%P'   // %P:  Parent hashes (space-separated)
  ].join(FIELD_SEPARATOR);

  // Construct the git log command. The `-z` flag separates commits with a null character,
  // which prevents issues with commit messages that contain newlines.
  const command = `git log ${head} -n ${count} --pretty=format:"${prettyFormat}" -z`;

  try {
    // Execute the command in the specified git repository directory
    const { stdout } = await execAsync(command, { cwd: gitRoot });

    // If there's no output, return an empty array
    if (!stdout) {
      return [];
    }

    // Parse the raw string output from the git command
    return stdout
      .trim() // Remove any leading/trailing whitespace
      .split(RECORD_SEPARATOR) // Split the output into individual commit records
      .filter(record => record) // Filter out any empty strings that may result from splitting
      .map(record => {
        // For each record, split it into its constituent fields
        const [commitId, timestampStr, author, message, parentsStr] = record.split(FIELD_SEPARATOR);

        // Parse parent commit IDs (space-separated)
        const parents = parentsStr ? parentsStr.split(' ').filter(p => p) : [];

        // Create and return a Commit object
        return {
          commitId: commitId as FlakinessReport.CommitId,
          timestamp: parseInt(timestampStr, 10) * 1000 as FlakinessReport.UnixTimestampMS,
          author,
          message,
          parents: parents as FlakinessReport.CommitId[],
          walkIndex: 0,
        };
      });
  } catch (error) {
    console.error(`Failed to list commits for repository at ${gitRoot}:`, error);
    // Re-throw the error to allow the calling function to handle it
    throw error;
  }
}
