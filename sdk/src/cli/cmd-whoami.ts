#!/usr/bin/env node
import { FlakinessSession } from '../flakinessSession.js';

export async function cmdWhoami() {
  // Try to read token from config file
  const session = await FlakinessSession.load();
  if (!session) {
    console.log('Not logged in. Run "flakiness login" first.');
    process.exit(1);
  }
  console.log(`Logged into ${session.endpoint()}`);

  const user = await session.api.user.whoami.GET();
  console.log(user);
}