#!/usr/bin/env node
import chalk from 'chalk';
import fs from 'fs/promises';
import path from 'path';
import { ReportUploader } from '../reportUploader.js';
import { resolveAttachmentPaths } from '../utils.js';

const warn = (txt: string) => console.warn(chalk.yellow(`[flakiness.io] WARN: ${txt}`));
const err = (txt: string) => console.error(chalk.red(`[flakiness.io] Error: ${txt}`));
const log = (txt: string) => console.log(`[flakiness.io] ${txt}`);

export async function cmdUpload(relativePaths: string[], options: {
  accessToken: string,
  endpoint: string,
  attachmentsDir?: string,
  ignoreMissingAttachments?: boolean,
}) {
  const uploader = new ReportUploader({
    flakinessAccessToken: options.accessToken,
    flakinessEndpoint: options.endpoint,
  });

  for (const relativePath of relativePaths) {
    const fullPath = path.resolve(relativePath);
    if (!(await fs.access(fullPath, fs.constants.F_OK).then(() => true).catch(() => false))) {
      err(`Path ${fullPath} is not accessible!`);
      process.exit(1);
    }
  
    // Read all files from attachments directory.
    const text = await fs.readFile(fullPath, 'utf-8');
    const report = JSON.parse(text);
    const attachmentsDir = options.attachmentsDir ?? path.dirname(fullPath);
    const { attachmentIdToPath, missingAttachments } = await resolveAttachmentPaths(report, attachmentsDir);
  
    if (missingAttachments.length) {
      warn(`Missing ${missingAttachments.length} attachments`);
    }
  
    const upload = uploader.createUpload(report, Array.from(attachmentIdToPath.values()));
    const uploadResult = await upload.upload();
    if (!uploadResult.success) {
      err(`Failed to upload to ${options.endpoint}: ${uploadResult.message}`);
    } else {
      log(`✓ Uploaded ${uploadResult.reportUrl ?? uploadResult.message ?? ''}`);
    }
  }
}
