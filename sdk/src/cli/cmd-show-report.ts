import chalk from 'chalk';
import open from "open";
import path from 'path';
import { FlakinessConfig } from "../flakinessConfig.js";
import { FlakinessSession } from "../flakinessSession.js";
import { LocalReportServer } from '../localReportServer.js';

export async function cmdShowReport(reportFolder: string) {
  const reportPath = path.join(reportFolder, 'report.json');
  const session = await FlakinessSession.load();
  const config = await FlakinessConfig.load();
  const projectPublicId = config.projectPublicId();
  const project = projectPublicId && session ? await session.api.project.getProject.GET({ projectPublicId }).catch(e => undefined) : undefined;

  const endpoint = session?.endpoint() ?? 'https://flakiness.io';
  const server = await LocalReportServer.create({
    endpoint,
    port: 9373,
    reportPath,
    attachmentsFolder: reportFolder,
  });
  const reportEndpoint = project
    ? `${endpoint}/localreport/${project.org.orgSlug}/${project.projectSlug}?port=${server.port()}&token=${server.authToken()}`
    : `${endpoint}/localreport?port=${server.port()}&token=${server.authToken()}`;
  console.log(chalk.cyan(`
  Serving Flakiness report at ${(reportEndpoint)}
  Press Ctrl+C to quit.`))
  await open(reportEndpoint);
  await new Promise(() => {});
}