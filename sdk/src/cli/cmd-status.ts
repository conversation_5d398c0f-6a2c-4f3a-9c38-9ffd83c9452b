import { FlakinessConfig } from '../flakinessConfig.js';
import { FlakinessSession } from '../flakinessSession.js';

export async function cmdStatus() {
  const session = await FlakinessSession.load();
  if (!session) {
    console.log(`user: not logged in`);
    return;
  }
  const user = await session.api.user.whoami.GET();
  console.log(`user: ${user.userName} (${user.userLogin})`);
  const config = await FlakinessConfig.load();
  const projectPublicId = config.projectPublicId();
  if (!projectPublicId) {
    console.log(`project: <not linked>`);
    return;
  }
  const project = await session.api.project.getProject.GET({ projectPublicId });
  console.log(`project: ${session.endpoint()}/${project.org.orgSlug}/${project.projectSlug}`);
}