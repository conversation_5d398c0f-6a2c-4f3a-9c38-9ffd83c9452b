#!/usr/bin/env node
import fs from 'fs/promises';
import path from 'path';
import { PlaywrightJSONReport } from '../playwrightJSONReport.js';
import { ReportUploader } from '../reportUploader.js';

export async function cmdUploadPlaywrightJson(relativePath: string, options: {
  accessToken: string,
  endpoint: string,
}) {
  const fullPath = path.resolve(relativePath);
  if (!(await fs.access(fullPath, fs.constants.F_OK).then(() => true).catch(() => false))) {
    console.error(`Error: path ${fullPath} is not accessible`);
    process.exit(1);
  }

  const text = await fs.readFile(fullPath, 'utf-8');
  const playwrightJson = JSON.parse(text);
  const { attachments, report, unaccessibleAttachmentPaths } = await PlaywrightJSONReport.parse(PlaywrightJSONReport.collectMetadata(), playwright<PERSON><PERSON>, {
    extractAttachments: true,
  });

  for (const unaccessibleAttachment of unaccessibleAttachmentPaths)
    console.warn(`WARN: cannot access attachment ${unaccessibleAttachment}`);

  const uploader = new ReportUploader({
    flakinessAccessToken: options.accessToken,
    flakinessEndpoint: options.endpoint,
  });

  const upload = uploader.createUpload(report, attachments);
  const uploadResult = await upload.upload();
  if (!uploadResult.success) {
    console.log(`[flakiness.io] X Failed to upload to ${options.endpoint}: ${uploadResult.message}`);
  } else {
    console.log(`[flakiness.io] ✓ Report uploaded ${uploadResult.reportUrl ?? uploadResult.message ?? ''}`);
  }
}
