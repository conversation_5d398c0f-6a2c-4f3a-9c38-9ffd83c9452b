import { KNOWN_CLIENT_IDS } from '@flakiness/server/common/knownClientIds.js';
import open from 'open';
import os from 'os';
import { FlakinessSession } from '../flakinessSession.js';
import { createServerAPI } from '../serverapi.js';
import { cmdLogout } from './cmd-logout.js';

export const DEFAULT_FLAKINESS_ENDPOINT = 'https://flakiness.io'

export async function cmdLogin(endpoint: string = DEFAULT_FLAKINESS_ENDPOINT) {
  // Logout first before logging in.
  await cmdLogout();

  const api = createServerAPI(endpoint);
  const data = await api.deviceauth.createRequest.POST({
    clientId: KNOWN_CLIENT_IDS.OFFICIAL_CLI,
    name: os.hostname(),
  });

  await open(new URL(data.verificationUrl, endpoint).href);
  console.log(`Please navigate to ${new URL(data.verificationUrl, endpoint)}`);

  let token;
  while (Date.now() < data.deadline) {
    await new Promise(x => setTimeout(x, 2000));
    
    const result = await api.deviceauth.getToken.GET({ deviceCode: data.deviceCode }).catch(e => undefined);
    if (!result) {
      console.error(`Authorization request was rejected.`);
      process.exit(1);
    }
    token = result.token;
    if (token)
      break;
  }
  if (!token) {
    console.log(`Failed to login.`);
    process.exit(1);
  }
  
  const session = new FlakinessSession({
    endpoint,
    token
  });
  try {
    const user = await session.api.user.whoami.GET();
    await session.save();
    console.log(`✓ Logged in as ${user.userName} (${user.userLogin})`);
  } catch (e) {
    const message = e instanceof Error ? e.message : String(e);
    console.error(`x Failed to login:`, message);
  }
  return session;
}