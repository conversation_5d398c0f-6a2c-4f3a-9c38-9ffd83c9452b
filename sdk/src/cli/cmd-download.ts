import type { WireTypes } from '@flakiness/server/common/wireTypes.js';
import fs from 'fs';
import path from 'path';
import { FlakinessSession } from '../flakinessSession.js';

export async function cmdDownload(session: FlakinessSession, project: WireTypes.Project, runId: number) {
  const urls = await session.api.run.downloadURLs.GET({
    orgSlug: project.org.orgSlug,
    projectSlug: project.projectSlug,
    runId,
  });

  const rootDir = `fkrun-${runId}`;
  if (fs.existsSync(rootDir)) {
    console.log(`Directory ${rootDir} already exists!`);
    return;
  }

  const attachmentsDir = path.join(rootDir, 'attachments');
  await fs.promises.mkdir(rootDir, { recursive: true });
  if (urls.attachmentURLs.length)
    await fs.promises.mkdir(attachmentsDir, { recursive: true });

  const response = await fetch(urls.reportURL);
  if (!response.ok)
    throw new Error(`HTTP error ${response.status} for report URL: ${urls.reportURL}`);

  // `fetch` automatically handles content-encoding (like brotli, gzip)
  const reportContent = await response.text();
  await fs.promises.writeFile(path.join(rootDir, 'report.json'), reportContent);

  const attachmentDownloader = async () => {
    while (urls.attachmentURLs.length) {
      const url = urls.attachmentURLs.pop()!;
      const response = await fetch(url);
      if (!response.ok)
        throw new Error(`HTTP error ${response.status} for attachment URL: ${url}`);
      const fileBuffer = Buffer.from(await response.arrayBuffer());
      const filename = path.basename(new URL(url).pathname);
      await fs.promises.writeFile(path.join(attachmentsDir, filename), fileBuffer);
    }
  }

  const workerPromises: Promise<void>[] = [];
  // Save attachments in 4 parallel threads.
  for (let i = 0; i < 4; ++i)
    workerPromises.push(attachmentDownloader());
  await Promise.all(workerPromises);
  console.log(`✔️ Saved as ${rootDir}`);
}