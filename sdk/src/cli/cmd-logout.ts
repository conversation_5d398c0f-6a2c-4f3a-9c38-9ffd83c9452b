import { FlakinessSession } from '../flakinessSession.js';

export async function cmdLogout() {
  const session = await FlakinessSession.load();
  if (!session)
    return;
  const currentSession = await session.api.user.currentSession.GET().catch(e => undefined);
  if (currentSession)
    await session.api.user.logoutSession.POST({ sessionId: currentSession.sessionPublicId }).catch(e => undefined);
  await FlakinessSession.remove();
}