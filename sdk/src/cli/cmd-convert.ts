#!/usr/bin/env node
import { FlakinessReport } from '@flakiness/report';
import fs from 'fs/promises';
import path from 'path';
import { parseJUnit } from '../junit.js';
import { gitCommitInfo, saveReportAndAttachments } from '../utils.js';

export async function cmdConvert(junitPath: string, options: { envName: string, commitId?: string, outputDir: string }) {
  const fullPath = path.resolve(junitPath);
  if (!(await fs.access(fullPath, fs.constants.F_OK).then(() => true).catch(() => false))) {
    console.error(`Error: path ${fullPath} is not accessible`);
    process.exit(1);
  }

  // Check if it's a file or directory
  const stat = await fs.stat(fullPath);
  let xmlContents: string[] = [];

  if (stat.isFile()) {
    // Single file
    const xmlContent = await fs.readFile(fullPath, 'utf-8');
    xmlContents.push(xmlContent);
  } else if (stat.isDirectory()) {
    // Directory - scan for XML files
    const xmlFiles = await findXmlFiles(fullPath);
    if (xmlFiles.length === 0) {
      console.error(`Error: No XML files found in directory ${fullPath}`);
      process.exit(1);
    }
    console.log(`Found ${xmlFiles.length} XML files`);
    
    for (const xmlFile of xmlFiles) {
      const xmlContent = await fs.readFile(xmlFile, 'utf-8');
      xmlContents.push(xmlContent);
    }
  } else {
    console.error(`Error: ${fullPath} is neither a file nor a directory`);
    process.exit(1);
  }

  let commitId: FlakinessReport.CommitId;
  if (options.commitId) {
    commitId = options.commitId as FlakinessReport.CommitId;
  } else {
    try {
      commitId = gitCommitInfo(process.cwd());
    } catch (e) {
      console.error('Failed to get git commit info. Please provide --commit-id option.');
      process.exit(1);
    }
  }

  const { report, attachments } = await parseJUnit(xmlContents, {
    commitId,
    defaultEnv: { name: options.envName },
    runStartTimestamp: Date.now() as FlakinessReport.UnixTimestampMS,
    runDuration: 0 as FlakinessReport.DurationMS,
  });

  await saveReportAndAttachments(report, attachments, options.outputDir);
  console.log(`✓ Saved to ${options.outputDir}`);
}

async function findXmlFiles(dir: string, result: string[] = []): Promise<string[]> {
  const entries = await fs.readdir(dir, { withFileTypes: true });
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    if (entry.isFile() && entry.name.toLowerCase().endsWith('.xml'))
      result.push(fullPath);
    else if (entry.isDirectory())
      await findXmlFiles(fullPath, result);
  }
  return result;
}
