import fs from 'fs/promises';
import os from 'os';
import path from 'path';
import { createServerAPI } from './serverapi.js';

type SessionConfig = {
  token: string,
  endpoint: string,
}

const CONFIG_DIR = (() => {
  const configDir = process.platform === 'darwin' 
    ? path.join(os.homedir(), 'Library', 'Application Support', 'flakiness')
    : process.platform === 'win32'
    ? path.join(os.homedir(), 'AppData', 'Roaming', 'flakiness')
    : path.join(os.homedir(), '.config', 'flakiness');
  return configDir;
})();
const CONFIG_PATH = path.join(CONFIG_DIR, 'config.json');

export class FlakinessSession {
  static async loadOrDie(): Promise<FlakinessSession> {
    const session = await FlakinessSession.load();
    if (!session)
      throw new Error(`Please login first with 'npx flakiness login'`);
    return session;
  }

  static async load() {
    const data = await fs.readFile(CONFIG_PATH, 'utf-8').catch(e => undefined);
    if (!data)
      return undefined;
    const json = JSON.parse(data) as SessionConfig;
    return new FlakinessSession(json);
  }

  static async remove() {
    await fs.unlink(CONFIG_PATH).catch(e => undefined);
  }

  public readonly api;

  constructor(private _config: SessionConfig) {
    this.api = createServerAPI(this._config.endpoint, { auth: this._config.token });
  }

  endpoint() {
    return this._config.endpoint;
  }

  path() {
    return CONFIG_PATH;
  }

  sessionToken() {
    return this._config.token;
  }

  async save() {
    await fs.mkdir(CONFIG_DIR, { recursive: true });
    await fs.writeFile(CONFIG_PATH, JSON.stringify(this._config, null, 2));
  }
}
