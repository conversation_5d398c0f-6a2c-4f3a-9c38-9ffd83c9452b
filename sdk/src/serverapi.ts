import type { AppRouter } from '@flakiness/server/node/api.js';
import { TypedHTTP } from '@flakiness/shared/common/typedHttp.js';
import { retryWithBackoff } from './utils.js';

export function createServerAPI(endpoint: string|URL, options?: {
  auth?: string,
  retries?: number[],
}) {
  endpoint += '/api/';
  const fetcher = options?.auth ? (url: string|URL, init: RequestInit) => fetch(url, {
    ...init,
    headers: {
      ...init.headers,
      'Authorization': `Bearer ${options.auth}`,
    },
  }) : fetch;
  if (options?.retries)
    return TypedHTTP.createClient<AppRouter>(endpoint, (url, init) => retryWithBackoff(() => fetcher(url, init), options.retries));
  return TypedHTTP.createClient<AppRouter>(endpoint, fetcher);
}