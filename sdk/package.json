{"name": "@flakiness/sdk", "version": "0.133.0", "private": false, "bin": {"flakiness": "./lib/cli/cli.js"}, "exports": {"./localReportApi": {"types": "./types/src/localReportApi.d.ts"}, "./uploader": {"types": "./types/src/reportUploader.d.ts", "import": "./lib/reportUploader.js", "require": "./lib/reportUploader.js"}, "./utils": {"types": "./types/src/utils.d.ts", "import": "./lib/utils.js", "require": "./lib/utils.js"}, "./playwright": {"types": "./types/src/playwrightJSONReport.d.ts", "import": "./lib/playwrightJSONReport.js", "require": "./lib/playwrightJSONReport.js"}, "./junit": {"types": "./types/src/junit.d.ts", "import": "./lib/junit.js", "require": "./lib/junit.js"}, "./playwright-test": {"types": "./types/src/playwright-test.d.ts", "import": "./lib/playwright-test.js", "require": "./lib/playwright-test.js"}}, "type": "module", "description": "", "types": "./types/index.d.ts", "scripts": {"build:all": "npm run build:win && npm run build:linux && npm run build:mac && npm run build:alpine && npm run build:mac_intel", "build:win": "bun build ./lib/cli/cli.js --compile --minify --target=bun-windows-x64 --outfile dist/flakiness-win-x64.exe", "build:linux": "bun build ./lib/cli/cli.js --compile --minify --target=bun-linux-x64 --outfile dist/flakiness-linux-x64", "build:alpine": "bun build ./lib/cli/cli.js --compile --minify --target=bun-linux-x64-musl --outfile dist/flakiness-linux-x64-alpine", "build:mac": "bun build ./lib/cli/cli.js --compile --minify --target=bun-darwin-arm64 --outfile dist/flakiness-macos-arm64", "build:mac_intel": "bun build ./lib/cli/cli.js --compile --minify --target=bun-darwin-x64 --outfile dist/flakiness-macos-x64"}, "keywords": [], "author": "Degu Labs, Inc", "license": "Fair Source 100", "devDependencies": {"@flakiness/server": "0.133.0", "@playwright/test": "^1.54.0", "@types/babel__code-frame": "^7.0.6", "@types/compression": "^1.8.1", "@types/express": "^4.17.20"}, "dependencies": {"@babel/code-frame": "^7.26.2", "@flakiness/report": "0.133.0", "@flakiness/shared": "0.133.0", "@rgrove/parse-xml": "^4.2.0", "body-parser": "^1.20.3", "chalk": "^5.6.2", "commander": "^13.1.0", "compression": "^1.8.1", "debug": "^4.3.7", "express": "^4.21.2", "express-async-errors": "^3.1.1", "open": "^10.2.0", "zod": "^3.25.23"}}