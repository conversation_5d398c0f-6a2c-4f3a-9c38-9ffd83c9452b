#!/usr/bin/env bash
set -e
set +x

trap "cd $(pwd -P)" EXIT
cd "$(dirname "$0")"
SCRIPT_PATH=$(pwd)

# 1. Configuration
DIST="${SCRIPT_PATH}/dist"
BUCKET_PATH="cli/latest" # The path inside your bucket

# 4. Pre-flight Checks
if ! command -v rclone >/dev/null; then
  echo "❌ Error: rclone is not installed."
  exit 1
fi

if [[ ! -f ../../.env.deploymentsecrets ]]; then
  echo "❌ Error: deployment secrets not found!"
  echo "Run 'config/config deploymentsecrets'"
  exit 1
fi
source ../../.env.deploymentsecrets

# 3. Configure Rclone via Environment Variables
# This creates an in-memory remote named 'target'
export RCLONE_CONFIG_TARGET_TYPE="s3"
export RCLONE_CONFIG_TARGET_PROVIDER="Cloudflare"
export RCLONE_CONFIG_TARGET_ACCESS_KEY_ID="$CLI_S3_ACCESS_KEY_ID"
export RCLONE_CONFIG_TARGET_SECRET_ACCESS_KEY="$CLI_S3_SECRET_ACCESS_KEY"
export RCLONE_CONFIG_TARGET_ENDPOINT="$CLI_S3_ENDPOINT"


if [ ! -d "$DIST" ]; then
  echo "❌ Error: dist directory does not exist. Please run ./build.sh all first."
  exit 1
fi

# 5. Compression (skip if already compressed)
echo "📦 Compressing binaries in $DIST..."
find "$DIST" -type f ! -name "*.gz" -exec gzip -9 -v {} \;

# 6. Upload. Use SYNC to drop some binaries if we no longer ship them.
echo "☁️  Uploading to target:$BUCKET_PATH..."
rclone sync "$DIST" "target:$BUCKET_PATH" --progress

# 7. Upload install.sh to bucket root
echo "📜 Uploading install.sh to bucket root..."
rclone copy "${SCRIPT_PATH}/assets/" "target:cli/"

echo "✅ Upload complete."

