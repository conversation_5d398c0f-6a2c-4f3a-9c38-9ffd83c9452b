{"name": "@flakiness/shared", "version": "0.133.0", "private": false, "description": "", "type": "module", "types": "./types/src", "main": "./lib", "keywords": [], "author": "Degu Labs, Inc", "license": "Fair Source 100", "scripts": {"test": "npx playwright test -c playwright.config.ts"}, "exports": {"./*": {"types": "./types/src/*", "import": "./lib/*", "require": "./lib/*"}}, "dependencies": {"debug": "^4.4.3", "stable-hash": "^0.0.6", "xxhash-wasm": "^1.1.0", "zod": "^3.25.23"}, "devDependencies": {"@playwright/test": "^1.56.0", "@types/express": "^4.17.20"}}