@import '@shoelace-style/shoelace/dist/themes/light.css';

@font-face {
  font-family: 'Mona Sans';
  src:
    url('./fonts/Mona-Sans.woff2') format('woff2 supports variations'),
    url('./fonts/Mona-Sans.woff2') format('woff2-variations');
  font-weight: 200 900;
  font-stretch: 75% 125%;
}

:root,
:host,
.sl-theme-light {
  /* we're a DENSE dashboard; let's change default 16px to 14px. */
  font-size: 14px;
  --sl-color-neutral-50: #f6f8fa;
  --sl-color-neutral-100: #eaeef2;
  --sl-color-neutral-200: #d0d7de;
  --sl-color-neutral-300: #afb8c1;
  --sl-color-neutral-400: #8c959f;
  --sl-color-neutral-500: #6e7781;
  --sl-color-neutral-600: #57606a;
  --sl-color-neutral-700: #424a53;
  --sl-color-neutral-800: #32383f;
  --sl-color-neutral-900: #24292f;
}

html {
  /* font-family: 'Mona Sans'; */
  font-family: var(--sl-font-sans);

  --fk-color-text: var(--sl-color-neutral-900);
  color: var(--fk-color-text);

  --fk-color-outcome-regressed: red;
  --fk-color-outcome-unexpected: #ac2622;
  --fk-color-outcome-expected: green;
  --fk-color-outcome-skipped: var(--sl-color-neutral-300);
  --fk-color-outcome-flaked: #ff9800;
  --fk-color-highlight: yellow;
  --fk-color-tablerow-hover: var(--sl-color-warning-50);

  --fk-shadow-floating-small: 0px 0px 0px 1px #d1d9e080, 0px 6px 12px -3px #25292e0a, 0px 6px 18px 0px #25292e1f;


  --fk-color-border: var(--sl-color-neutral-200);
  --fk-color-canvas: var(--sl-color-neutral-50);
}

html, body {
  padding: 0;
  margin: 0;
}