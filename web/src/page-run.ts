import type { LocalReportAppRouter } from '@flakiness/sdk/localReportApi';
import { LitElement, css, html, nothing } from 'lit';
import { customElement, property, query } from 'lit/decorators.js';

import { Task, TaskStatus } from '@lit/task';

import { FlakinessReport, ReportUtils } from '@flakiness/report';
import { SyncComputationCache } from '@flakiness/server/common/computationCache.js';
import { Query } from '@flakiness/server/common/fql/query.js';
import { Histogram } from '@flakiness/server/common/stats/histogram.js';
import { SpanAnalyzer, SpanStats } from '@flakiness/server/common/stats/spanAnalyzer.js';
import { Stats } from '@flakiness/server/common/stats/stats.js';
import { StatsAnalyzer } from '@flakiness/server/common/stats/statsAnalyzer.js';
import { StatsBuilder } from '@flakiness/server/common/stats/statsBuilder.js';
import { FilterContext, TestIndex } from '@flakiness/server/common/stats/testIndex.js';
import { TestOutcomes as T } from '@flakiness/server/common/stats/testOutcomes.js';
import { TestsReport } from '@flakiness/server/common/stats/testsReport.js';
import { TimelineTestsReport } from '@flakiness/server/common/stats/timelineTestsReport.js';
import { Timeline } from '@flakiness/server/common/timeline/timeline.js';
import { TimelineSplit } from '@flakiness/server/common/timeline/timelineSplit.js';
import { WireReport } from '@flakiness/server/common/wireReport.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { TypedHTTP } from '@flakiness/shared/common/typedHttp.js';
import { ContextType } from '@lit/context';
import { timeDay } from 'd3';
import { api } from './api.js';
import { linkStyles, pageStyles } from './components/cssstyles.js';
import { DataTableLinker } from './components/data-table.js';
import { ExecutionHistory } from './components/execution-history.js';
import { FqlChangedEvent } from './components/fql-search.js';
import { ProjectAlerts } from './components/project-alerts.js';
import { ProjectContext } from './components/project-context.js';
import { ContextTask, consume, contexts } from './contexts.js';
import { createAnnotationStatsDataProvider, createAnnotationStatsLoaderLocal } from './data-table-providers/annotationsProvider.js';
import { createErrorStatsDataProvider, createErrorStatsLoaderLocal } from './data-table-providers/errorsProvider.js';
import { createDayDurationChartsColumn } from './data-table-providers/genericColumns.js';
import { createTagStatsDataProvider, createTagStatsLoaderLocal } from './data-table-providers/tagsProvider.js';
import { createTestStatsDataProvider, createTestStatsLoaderLocal } from './data-table-providers/testStatsProvider.js';
import { createTimelineStatsDataProvider, createTimelineStatsLoaderLocal } from './data-table-providers/timelineStatsProvider.js';
import { PageReport } from './page-report.js';
import { RouteConfig, URLState } from './router.js';
import { HISTORY_SNIPPET_DAYS, REGRESSION_WINDOW_DAYS, assert, midday, wireDays } from './utils.js';

type PlaneName = 'tests'|'timelines'|'errors'|'tags'|'annotations';

const dashboardState = new URLState({
  query: URLState.option<string>({
    name: 'q',
    default: '',
    encode: filter => filter,
    decode: filter => filter,
  }),
  testId: URLState.option<Stats.TestId|undefined>({
    name: 'test_id',
    default: undefined,
    encode: testId => testId,
    decode: testId => testId ? testId as Stats.TestId : undefined,
  }),
  testTimeline: URLState.option<Timeline|undefined>({
    name: 'test_timeline',
    default: undefined,
    encode: timeline => timeline ? JSON.stringify(timeline.serialize()) : undefined,
    decode: text => Timeline.deserialize(JSON.parse(text) as WireTypes.JSONTimeline),
  }),
  testAttempt: URLState.option<number>({
    name: 'test_attempt',
    default: 0,
    encode: attemptIdx => attemptIdx ? String(attemptIdx) : undefined,
    decode: attemptText => attemptText ? parseInt(attemptText, 10) : 0,
  }),
  plane: URLState.option<PlaneName|undefined>({
    name: 'tab',
    default: undefined,
    encode: plane => plane,
    decode: plane => ['tests', 'timelines', 'errors', 'tags', 'annotations'].includes(plane) ? plane as PlaneName : undefined,
  }),
  timelineSplit: URLState.option<TimelineSplit>({
    name: 'split',
    default: TimelineSplit.DEFAULT,
    encode: split => JSON.stringify(split.serialize()),
    decode: text => TimelineSplit.deserialize(JSON.parse(text) as WireTypes.JSONTimelineSplit),
  }),
  page: URLState.option<number>({
    name: 'page',
    default: 0,
    // keep pages one-based in url for user's pleasure.
    encode: page => String(page + 1),
    decode: page => isNaN(parseInt(page, 10)) ? 1 : parseInt(page, 10) - 1,
  }),
  pageSize: URLState.option<number>({
    name: 'page_size',
    default: 20,
    encode: pageSize => String(pageSize),
    decode: pageSize => isNaN(parseInt(pageSize, 10)) ? 1 : parseInt(pageSize, 10),
  }),
  sortName: URLState.option<string|undefined>({
    name: 'sort_by',
    default: undefined,
    encode: sort => sort,
    decode: sort => sort,
  }),
  sortDirection: URLState.option<WireTypes.SortDirection|undefined>({
    name: 'sort_dir',
    default: undefined,
    encode: sort => sort,
    decode: sort => sort === 'asc' || sort === 'desc' ? sort as WireTypes.SortDirection : undefined,
  }),

  // --- local report state ---

  port: URLState.option<number|undefined>({
    name: 'port',
    default: undefined,
    encode: number => String(number),
    decode: text => text && !isNaN(parseInt(text, 10)) ? parseInt(text, 10) : undefined,
  }),
  token: URLState.option<string|undefined>({
    name: 'token',
    default: undefined,
    encode: token => String(token),
    decode: token => token.trim().length > 0 ? token : undefined,
  }),
});

@customElement('page-run')
export class PageRun extends LitElement {
  static routes(): RouteConfig[] {
    return [{
      path: [
        '/:org/:project/run/:reportId',
        '/:org/:project/run/:reportId/',
      ],
      render: (groups) => ProjectContext.render({
        orgSlug: groups?.org,
        projectSlug: groups?.project,
      }, html`
        <page-run
          .orgSlug=${groups?.org}
          .projectSlug=${groups?.project}
          .reportId=${groups?.reportId ? parseInt(groups.reportId) : undefined}
          ?local=${false}
        ></page-run>
      `),
    }, {
      path: [
        '/localreport',
        '/localreport/',
        '/localreport/:org/:project',
        '/localreport/:org/:project/',
      ],
      render: (groups) => ProjectContext.render({
        orgSlug: groups?.org,
        projectSlug: groups?.project,
      }, html`
        <page-run
          .orgSlug=${groups?.org}
          .projectSlug=${groups?.project}
          .reportId=${undefined}
          ?local=${true}
        ></page-run>
      `),
    }];
  }

  static url(options: {
    orgSlug?: string, 
    projectSlug?: string,
    reportId?: Stats.RunId,
    plane?: PlaneName,
    testId?: Stats.TestId,
    testTimeline?: Timeline,
    attemptIdx?: number,
    query?: string
    timelineSplit?: TimelineSplit,
    port?: number,
    token?: string,
  }) {
    if (options.reportId) {
      const linker = dashboardState.createLinkRenderer({
        pathname: ['', options.orgSlug, options.projectSlug, 'run', options.reportId].join('/'),
      });
      return linker({
        query: options.query?.trim() ? options.query : undefined,
        plane: options.plane,
        timelineSplit: options.timelineSplit,
        testId: options.testId,
        testAttempt: options.attemptIdx,
        testTimeline: options.testTimeline,
      });
    }
    const pathname = options.orgSlug && options.projectSlug
      ? ['', 'localreport', options.orgSlug, options.projectSlug].join('/')
      : ['', 'localreport'].join('/')
    ;
    const linker = dashboardState.createLinkRenderer({ pathname });
    return linker({
      port: options.port,
      token: options.token,
      query: options.query?.trim() ? options.query : undefined,
      plane: options.plane,
      timelineSplit: options.timelineSplit,
      testId: options.testId,
      testAttempt: options.attemptIdx,
      testTimeline: options.testTimeline,
    });
  }

  private _urlState = dashboardState.bind(this, () => {
    this._executionHistory?.hide();
  });

  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;
  @consume({ context: contexts.user, subscribe: true }) private _user?: ContextType<typeof contexts.user>;
  @consume({ context: contexts.router, subscribe: true }) private _router?: ContextType<typeof contexts.router>;
  @consume({ context: contexts.projectReferences, subscribe: true }) private _branches?: ContextType<typeof contexts.projectReferences>;

  @property({ attribute: false }) projectSlug?: string;
  @property({ attribute: false }) orgSlug?: string;
  @property({ attribute: false }) reportId?: Stats.RunId;
  @property({ type: Boolean }) local?: boolean;

  private _localApi = new Task(this, {
    args: () => [this._urlState.port, this._urlState.token, this.local] as const,
    task: async ([port, token, isLocal], { signal }) => {
      assert(port && token && isLocal);
      const api = TypedHTTP.createClient<LocalReportAppRouter>(`http://localhost:${port}/${token}/`, fetch);
      // check local server is up & running.
      await api.ping.GET();
      return api;
    }
  });

  private _filterCache = new SyncComputationCache<{ testIndex: TestIndex, fql: Query }, FilterContext> ({
    size: 10,
    etag: ({ testIndex, fql }) => TestIndex.etagFilterContext(testIndex, fql),
    compute: ({ testIndex, fql }, etag) => {
      return testIndex.createFilterContext(fql, etag);
    }
  });

  private _dataTableLinker = new Task(this, {
    args: () => [window.location.href],
    task: async ([], { signal }) => {
      // All self-links depend on self-href to be up-to-date.
      const linkRenderer = dashboardState.createLinkRenderer({ base: this._urlState });
      return ((state) => linkRenderer(state)) satisfies DataTableLinker;
    }
  });

  private _fqlTask = new ContextTask(this, contexts.fql, {
    args: () => [this._urlState.query] as const,
    task: async ([query], { signal }) => {
      return Query.parse(query ?? '');
    }
  });

  private _fqlLinker = new ContextTask(this, contexts.linkFQL, {
    args: () => [window.location.href],
    task: async ([], { signal }) => {
      // All self-links depend on self-href to be up-to-date.
      const linkRenderer = dashboardState.createLinkRenderer({ base: this._urlState });
      return {
        render: (query) => linkRenderer({ query, page: 0, }),
      }
    }
  });

  private _timelineLinker = new ContextTask(this, contexts.linkTimeline, {
    args: () => [window.location.href],
    task: async ([], { signal }) => {
      // All self-links depend on self-href to be up-to-date.
      const linkRenderer = dashboardState.createLinkRenderer({ base: this._urlState });
      return {
        render: (timeline) => linkRenderer({ timelineSplit: TimelineSplit.fromTimeline(timeline) }),
      }
    }
  });

  private _commitTask = new Task(this, {
    args: () => [this.orgSlug, this.projectSlug, this._wireReportTask.value, this._localApi.value] as const,
    task: async ([orgSlug, projectSlug, wireReport, localApi], { signal }) => {
      assert(wireReport);
      if (localApi) {
        const commits = await localApi.lastCommits.GET();
        return commits[0];
      }

      assert(orgSlug && projectSlug);
      const page = await api.project.listCommits.GET({
        orgSlug, projectSlug,
        commitOptions: {
          head: wireReport.commitId,
          maxCount: 1,
        },
        pageOptions: { number: 0, size: 1 },
      }, { signal });

      // This might return `undefined` if we failed to fetch
      // commit from the backend.
      // In this case, there'll be no history.
      return page.elements[0];
    }
  });

  private _historyTask = new Task(this, {
    args: () => [this._commitTask.value, this._project,
      this._branches, this._wireReportTask.value,
      this._testIndexTask.value,
      this._timelinesTask.value,
    ] as const,
    task: async ([commit, project, branches, wireReport, testIndex, timelines], { signal }) => {
      assert(commit && project && testIndex && timelines);
      const until = timeDay.ceil(new Date(commit.timestamp));
      const since = timeDay.offset(until, -HISTORY_SNIPPET_DAYS);
      const historyDays = timeDay.range(since, until).map(midday).reverse();

      assert(wireReport);
      const historyHead = (this.local && branches) ? branches.defaultBranch.commit.commitId : wireReport.commitId;

      const timelineToCommitOutcomes = new Map<string, T.TestOutcomes>();
      const timelineToDailyStats = new Map<string, SpanStats[]>();
      const [commitOutcomesResponse, dailyOutcomesResponse] = await Promise.all([
        api.report.commitUnfilteredExpandedTestOutcomes.POST({
          orgSlug: project.org.orgSlug,
          projectSlug: project.projectSlug,
          days: wireDays({
            backfillDays: REGRESSION_WINDOW_DAYS,
            since: new Date(commit.timestamp),
            until: new Date(commit.timestamp),
          }),
          cutoffTestIndex: testIndex.cutoffIndex(),
          regressionWindowDays: REGRESSION_WINDOW_DAYS,
          commitId: commit.commitId,
          timelines: timelines.map(t => t.serialize()),
        }, { signal }),
        api.report.dailyUnfilteredExpandedTestOutcomes.GET({
          orgSlug: project.org.orgSlug,
          projectSlug: project.projectSlug,
          head: historyHead,
          days: wireDays({
            since: historyDays.at(-1)!,
            until: historyDays[0],
            backfillDays: REGRESSION_WINDOW_DAYS,
          }),
          cutoffTestIndex: testIndex.cutoffIndex(),
          regressionWindowDays: REGRESSION_WINDOW_DAYS,
          timelines: timelines.map(t => t.serialize()),
        }, { signal })
      ]);
      for (const { timeline, testOutcomes } of commitOutcomesResponse)
        timelineToCommitOutcomes.set(Timeline.deserialize(timeline).etag(), testOutcomes);
      for (const { timeline, testOutcomes, commitCounts, durations } of dailyOutcomesResponse) {
        const dailyStats: SpanStats[] = commitCounts.slice(0, historyDays.length).map((commitsCount, idx) => ({
          commitsCount,
          testOutcomes: testOutcomes[idx],
          durations: durations[idx].map(h => Histogram.decompress(h)),
        }));
        timelineToDailyStats.set(Timeline.deserialize(timeline).etag(), dailyStats);
      }
      return {
        timelinesHistory: timelines.map(timeline => ({
          timeline,
          commitOutcomes: timelineToCommitOutcomes.get(timeline.etag()) ?? T.newOutcomes(),
          dailyOutcomes: timelineToDailyStats.get(timeline.etag()) ?? [],
        })),
        historyDays,
      };
    }
  });

  private _testIndexTask = new Task(this, {
    args: () => [this.local, this.orgSlug, this.projectSlug] as const,
    task: async ([local, orgSlug, projectSlug], { signal }) => {
      const jsonTestIndex = orgSlug && projectSlug ? await api.report.testIndex.GET({
        orgSlug, projectSlug,
      }, { signal }) : local ? Stats.createEmptyTests() : undefined;
      assert(jsonTestIndex);
      return new TestIndex(jsonTestIndex);
    }
  });

  private _wireReportTask = new Task(this, {
    args: () => [
      this._project, this.reportId, this._localApi.value,
    ] as const,
    task: async ([project, reportId, localApi], { signal }) => {
      assert((project && reportId !== undefined) || localApi);
      // First, fetch report.
      const json = reportId && project ? await api.run.json.GET({
        orgSlug: project.org.orgSlug,
        projectSlug: project.projectSlug,
        reportId: reportId,
      }, { signal }) : await localApi?.report.json.GET(undefined, { signal });
      assert(json);
      return new WireReport(ReportUtils.dedupeSuitesTestsEnvironments(json));
    }
  });

  private _timelinesTask = new Task(this, {
    args: () => [
      this._wireReportTask.value, this._urlState.timelineSplit, this._urlState.testTimeline
    ] as const,
    task: async ([wireReport, timelinesSplit, testTimeline], { signal }) => {
      assert(wireReport && timelinesSplit);
      const timelines = testTimeline ? [testTimeline] : timelinesSplit.timelines(wireReport.envs);
      return timelines;
    }
  });

  private _statsTask = new Task(this, {
    args: () => [this.reportId, this._wireReportTask.value, this._testIndexTask.value] as const,
    task: async ([reportId, wireReport, testIndex ], { signal }) => {
      assert(wireReport && testIndex);
      const builder = StatsBuilder.create(testIndex, undefined);
      testIndex.addReport(wireReport.report);
      builder.addRun(reportId ?? 0 as Stats.RunId, wireReport.report, {
        durationCompressionMaxRelativeError: 0,
        durationCompressionMinAbsoluteErrorMs: 0,
      });
      const stats = new StatsAnalyzer(builder.jsonStats());
      return stats;
    }
  });

  private _testStatsTask = new Task(this, {
    args: () => [ this._testsReportTask.value, this._urlState.testId, this._urlState.testTimeline ] as const,
    task: async ([report, testId, testTimeline ], { signal }) => {
      assert(report && testId && testTimeline);
      return report.findTestStats(testId, testTimeline);
    }
  });

  private _testLinkRenderer = new ContextTask(this, contexts.linkTest, {
    args: () => [] as const,
    task: async ([], { signal }) => {
      return {
        render: (testStats) => PageRun.url({
          orgSlug: this.orgSlug,
          projectSlug: this.projectSlug,
          timelineSplit: this._urlState.timelineSplit,
          plane: this._urlState.plane,
          query: this._urlState.query,
          testTimeline: Timeline.deserialize(testStats.timeline),
          reportId: this.local ? undefined : testStats.runId,
          testId: testStats.test.testId,
          attemptIdx: testStats.attemptIdx,
          port: this._urlState.port,
          token: this._urlState.token,
        }),
      }
    }
  });

  private _unhealthyTests(commitOutcomes: T.TestOutcomes) {
    // Local reports have not being aggregated to the commit yet.
    // In this case, we take unhealthy tests from the commit outcomes.
    // However, non-local, aggregated report computes unhealthy run tests using
    // the T.unhealthyTestsForAggregatedRun primitive.
    return this.local && this._user && this.orgSlug && this.projectSlug ? T.unhealthyTests(commitOutcomes) : T.unhealthyTestsForAggregatedRun(commitOutcomes);
  }

  private _testsReportTask = new Task(this, {
    args: () => [
      this._statsTask.value, this._fqlTask.value, this._timelinesTask.value, this._wireReportTask.value, this._testIndexTask.value,
      this._historyTask.value,
    ] as const,
    task: async ([stats, fql, timelines, wireReport, testIndex, history], { signal }) => {
      assert(stats && fql && timelines && wireReport && testIndex);
      const span = new SpanAnalyzer([stats.getCommitAnalyzer(wireReport.commitId)]);
      const filter = this._filterCache.get({ testIndex, fql });
      const timelineTestsReports = history ? history.timelinesHistory.map(timelineHistory => {
        const unhealthyRunTests = this._unhealthyTests(timelineHistory.commitOutcomes);
        return TimelineTestsReport.create(timelineHistory.timeline, span, unhealthyRunTests).applyFilter(filter).setIntermediateStats(timelineHistory.dailyOutcomes);
      }) : timelines.map(timeline => {
        return TimelineTestsReport.create(timeline, span, T.EMPTY_TESTS).applyFilter(filter);
      })
      return new TestsReport(testIndex, timelineTestsReports);
    }
  });

  private _countersTask = new Task(this, {
    args: () => [
      this._testsReportTask.value, this._fqlTask.value, this._testIndexTask.value
    ] as const,
    task: async ([testsReport, fql, testIndex], { signal }) => {
      assert(testsReport && fql && testIndex);
      const filter = this._filterCache.get({ testIndex, fql: fql.clearStatusFilters() });
      return testsReport.applyFilter(filter).totalOutcomes();
    }
  });

  private _testRunAttemptsTask = new Task(this, {
    args: () => [
      this._wireReportTask.value, this._testStatsTask.value,
    ] as const,
    task: async ([wireReport, testStats], { signal }) => {
      assert(wireReport && testStats);

      const test = wireReport.fkTest(testStats.test.testId);
      assert(test);
      const attempts = test.attempts.filter(attempt => wireReport.envs[attempt.environmentIdx].envId === testStats.envId);
      assert(attempts.length);
      return attempts;
    },
  });

  private _dataTableProviderTask = new Task(this, {
    args: () => [
      this._wireReportTask.value, this._testsReportTask.value, this._urlState.plane, this._historyTask.value, this._commitTask.value
    ] as const,
    task: async ([wireReport, testsReport, plane, history, commit], { signal }) => {
      if (!wireReport || !testsReport)
        return;
      plane ??= 'tests';
      if (plane === 'tests') {
        const durationsChartColumn = history && commit ? createDayDurationChartsColumn<WireTypes.TestStats>({
          dayStats: testStats => testStats.daily,
          days: history.historyDays,
          onHistoryDaySelected: (testStats, selectedDay) => this._executionHistory?.show({
            since: timeDay.floor(selectedDay),
            until: timeDay.offset(timeDay.floor(selectedDay), 1),
            testStats,
            timeline: Timeline.deserialize(testStats.timeline),
            head: { type: 'commit', name: commit.commitId, commit },
          }),
        }) : undefined;
        return createTestStatsDataProvider({
          loader: createTestStatsLoaderLocal(testsReport),
          durationsChartColumn,
          sort: 'outcome',
          runLinker: (testStats) => PageRun.url({
            orgSlug: this.orgSlug,
            projectSlug: this.projectSlug,
            reportId: testStats.runId,
            testTimeline: Timeline.deserialize(testStats.timeline),
            testId: testStats.test.testId,
            attemptIdx: testStats.attemptIdx,
            timelineSplit: TimelineSplit.fromTimeline(Timeline.deserialize(testStats.timeline)),
          }),
        });
      }
      if (plane === 'timelines') {
        const durationsChartColumn = history && commit ? createDayDurationChartsColumn<WireTypes.TimelineStats>({
          dayStats: stats => stats.daily,
          days: history.historyDays,
          onHistoryDaySelected: (timelineStats, selectedDay) => this._executionHistory?.show({
            since: timeDay.floor(selectedDay),
            until: timeDay.offset(timeDay.floor(selectedDay), 1),
            timeline: Timeline.deserialize(timelineStats.timeline),
            head: { type: 'commit', name: commit.commitId, commit },
          }),
        }) : undefined;
        return createTimelineStatsDataProvider({ 
          loader: createTimelineStatsLoaderLocal(testsReport),
          durationsChartColumn,
        });
      }
      if (plane === 'errors')
        return createErrorStatsDataProvider({ loader: createErrorStatsLoaderLocal(testsReport) });
      if (plane === 'tags')
        return createTagStatsDataProvider({ loader: createTagStatsLoaderLocal(testsReport) });
      if (plane === 'annotations')
        return createAnnotationStatsDataProvider({ loader: createAnnotationStatsLoaderLocal(testsReport) });
    }
  });

  @query('execution-history') private _executionHistory?: ExecutionHistory;

  private _renderHeader() {
    return this.local ? html`
      <localreport-header
        .orgSlug=${this.orgSlug}
        .projectSlug=${this.projectSlug}
      ></localreport-header>
    ` : html`
      <project-header
        .orgSlug=${this.orgSlug}
        .projectSlug=${this.projectSlug}
        .submenu=${"runs"}
      ></project-header>
    `;
  }

  private _renderLoadingStub() {
    return html`
      ${this._renderHeader()}
      <app-body wide>
        <sl-animation name=fadeIn easing=easeIn play delay=500 duration=500 iterations=1 fill=forwards>
          <v-box style="gap: var(--sl-spacing-2x-large); opacity: 0;">
            <!-- Worker chart -->
            <v-box>
              <sl-skeleton effect="pulse" style="height: 10px;"></sl-skeleton>
              <sl-skeleton effect="pulse" style="height: 10px;"></sl-skeleton>
            </v-box>

            <!-- Header -->
            <h-box style="align-items: start;">
              <sl-skeleton effect="pulse" style="font-size: var(--sl-font-size-3x-large); width: 7em; height: 3em;"></sl-skeleton>
              <x-filler></x-filler>
              <sl-skeleton effect="pulse" style="font-size: var(--sl-font-size-2x-large); width: 12em; height: 2em;"></sl-skeleton>
            </h-box>

            <!-- FQL -->
            <sl-skeleton effect="pulse" style="font-size: var(--sl-font-size-2x-large); height: 1em;"></sl-skeleton>

            <!-- summary -->
            <h-box>
              <sl-skeleton effect="pulse" style="font-size: var(--sl-font-size-2x-large); height: 2em; width: 7ch;"></sl-skeleton>
              <sl-skeleton effect="pulse" style="font-size: var(--sl-font-size-2x-large); height: 2em; width: 7ch;"></sl-skeleton>
              <sl-skeleton effect="pulse" style="font-size: var(--sl-font-size-2x-large); height: 2em; width: 7ch;"></sl-skeleton>
              <sl-skeleton effect="pulse" style="font-size: var(--sl-font-size-2x-large); height: 2em; width: 7ch;"></sl-skeleton>
              <sl-skeleton effect="pulse" style="font-size: var(--sl-font-size-2x-large); height: 2em; width: 7ch;"></sl-skeleton>
            </h-box>
            <!-- table -->
            <v-box style="gap: 0;">
              <!-- table tabs -->
              <h-box>
                <sl-skeleton effect="pulse" style="font-size: var(--sl-font-size-medium); height: 2em; width: 7ch;"></sl-skeleton>
                <sl-skeleton effect="pulse" style="font-size: var(--sl-font-size-medium); height: 2em; width: 7ch;"></sl-skeleton>
                <sl-skeleton effect="pulse" style="font-size: var(--sl-font-size-medium); height: 2em; width: 7ch;"></sl-skeleton>
                <x-filler></x-filler>
                <sl-skeleton effect="pulse" style="font-size: var(--sl-font-size-medium); height: 2em; width: 12ch;"></sl-skeleton>
              </h-box>
              <sl-divider></sl-divider>
              <sl-skeleton effect="pulse" style="height: 750px; --border-radius: var(--sl-border-radius-large);"></sl-skeleton>
            </v-box>
          </v-box>
        </sl-animation>
      </app-body>
    `;
  }

  private _renderErrorLoadingReport() {
    if (!this.local)
      return html`<div>Error loading report</div>`;
    return html`
      ${this._renderHeader()}
      <app-body>
        <h3>Error loading report.</h3>
      </app-body>
    `;
  }

  override render() {
    if (this._wireReportTask.status !== TaskStatus.COMPLETE) {
      return this._wireReportTask.render({
        pending: () => this._renderLoadingStub(),
        error: () => this._renderErrorLoadingReport(),
      });
    }

    const wireReport = this._wireReportTask.value;

    const link = dashboardState.createLinkRenderer({
      base: this._urlState,
    });
    const commit = this._commitTask.value;
    const testId = this._urlState.testId;
    const testStats = this._testStatsTask.value;
    const history = this._historyTask.value;

    return html`
      <execution-history></execution-history>

      ${this._renderHeader()}

      <app-body wide>
        ${ProjectAlerts.maybeRender(this._project)}

        <chart-run-workers
          .report=${wireReport?.report}
          .testRun=${(this._testRunAttemptsTask.value ?? []).at(this._urlState.testAttempt)}
          @fk-testrun-selected=${(event: CustomEvent<{ report: FlakinessReport.Report, test: FlakinessReport.Test, attempt: FlakinessReport.RunAttempt, }|undefined>) => {
            if (!event.detail) {
              this._urlState.testId = undefined;
              this._urlState.testAttempt = 0;
              this._urlState.testTimeline = undefined;
            } else {
              const { report, test, attempt } = event.detail;
              const wireReport = new WireReport(report);
              const timeline = this._urlState.timelineSplit.timelines([wireReport.envs[event.detail.attempt.environmentIdx]])[0];
              const allAttempts = test.attempts.filter(a => timeline.acceptsEnvironment(wireReport.envs[a.environmentIdx]));
              this._urlState.testId = wireReport.wireTest(test)!.testId;
              this._urlState.testTimeline = timeline;
              this._urlState.testAttempt = allAttempts.indexOf(attempt);
            }
          }}
        ></chart-run-workers>
        <h-box>
          <v-box style="gap: var(--sl-spacing-2x-small);">
            <!-- Render report title: either a local report or cloud report. -->
            ${this.reportId ? html`
              <a href=${PageRun.url({
                orgSlug: this.orgSlug,
                projectSlug: this.projectSlug,
                reportId: this.reportId,
                port: this._urlState.port,
                token: this._urlState.token,
              })} class=title>Run #${this.reportId}</a>
            ` : html`
              <a href=${PageRun.url({
                orgSlug: this.orgSlug,
                projectSlug: this.projectSlug,
                port: this._urlState.port,
                token: this._urlState.token,
              })} class=title>Local Report</a>
            `}
            ${wireReport ? html`
              <div class=details>
                <a-ext href=${wireReport.url ?? nothing}><sl-format-date month="long" day="numeric" year="numeric" hour="numeric" minute="numeric" date=${new Date(wireReport.report.startTimestamp ?? Date.now()).toISOString()}></sl-format-date></a-ext>
                <time-interval icon .ms=${wireReport.duration}></time-interval>
              </div>
            ` : nothing}
          </v-box>
          <x-filler></x-filler>
          ${commit ? html`
            <div class=second-column>
              <fk-commit .commit=${commit} href=${
                this.orgSlug && this.projectSlug ? PageReport.url({
                  orgSlug: this.orgSlug,
                  projectSlug: this.projectSlug,
                  commitsCount: 1,
                  head: commit.commitId,
                }) : nothing
              }></fk-commit>
            </div>
          ` : nothing}
        </h-box>

        <timeline-selector multiple
          .envs=${wireReport?.envs}
          .split=${this._urlState.timelineSplit}
          @fk-select=${(event: CustomEvent<TimelineSplit>) => {
            this._urlState.timelineSplit = event.detail;
          }}
        ></timeline-selector>

        ${testId ? this._renderTest({ testStats, report: wireReport?.report }) : html`
          <h-box style="align-items: flex-start;">
            <fql-search q=${this._urlState.query} @fql-changed=${(e: FqlChangedEvent) => {
              this._urlState.query = e.query;
              this._urlState.page = 0;
            }}></fql-search>

            <status-selector ?hide-regressions=${!history}></status-selector>
          </h-box>

          <report-outcomes ?hide-regressions=${!history} .outcomes=${this._countersTask.value}></report-outcomes>

          <fk-tabbar selected=${this._urlState.plane ?? 'tests'}>
            <fk-tab
              name=tests
              data-testid=plane-tests
              href=${link({ plane: 'tests', page: 0, sortDirection: undefined, sortName: undefined, })}
            >
              Tests (${this._testsReportTask.value?.counters()?.tests ?? 0})
            </fk-tab>
            <fk-tab
              name=timelines
              data-testid=plane-timelines
              href=${link({ plane: 'timelines', page: 0, sortDirection: undefined, sortName: undefined, })}
            >
              Timelines (${this._testsReportTask.value?.counters()?.timelines ?? 0})
            </fk-tab>
            <fk-tab
              name=errors
              data-testid=plane-errors
              href=${link({ plane: 'errors', page: 0, sortDirection: undefined, sortName: undefined, })}
            >
              Errors (${this._testsReportTask.value?.counters()?.errors ?? 0})
            </fk-tab>
            <fk-tab
              name=tags
              data-testid=plane-tags
              href=${link({ plane: 'tags', page: 0, sortDirection: undefined, sortName: undefined, })}
            >
              Tags (${this._testsReportTask.value?.counters()?.tags ?? 0})
            </fk-tab>
            <fk-tab
              name=annotations
              data-testid=plane-annotations
              href=${link({ plane: 'annotations', page: 0, sortDirection: undefined, sortName: undefined, })}
            >
              Annotations (${this._testsReportTask.value?.counters()?.annotations ?? 0})
            </fk-tab>

            <fk-tabpane>
              <data-table
                .pageNumber=${this._urlState.page}
                .pageSize=${this._urlState.pageSize}
                .sortAxis=${this._urlState.sortName}
                .sortDirection=${this._urlState.sortDirection}
                .provider=${this._dataTableProviderTask.value}
                .linker=${this._dataTableLinker.value}
              ></data-table>
            </fk-tabpane>
          </fk-tabbar>
        `}
      </app-body>
      <app-footer></app-footer>
    `;
  }

  private _renderTest(options: {
    report?: FlakinessReport.Report,
    testStats?: WireTypes.TestStats,
  }) {
    const localApi = this._localApi.value;
    const { report, testStats } = options;
    if (!report || !testStats)
      return nothing;
    const commit = this._commitTask.value;
    const history = this._historyTask.value;
    const reportId = this.reportId;
    const orgSlug = this.orgSlug;
    const projectSlug = this.projectSlug;
    if (!localApi && (!reportId || !orgSlug || !projectSlug))
      return nothing;
    const attachmentURLBuilder = (attachmentId: FlakinessReport.AttachmentId, isPlaywrightTrace: boolean) => (reportId && orgSlug && projectSlug && !isPlaywrightTrace) ? api.run.attachment.GET.prepare({
        attachmentId,
        orgSlug,
        projectSlug,
        reportId,
      }).url.toString()
      : (reportId && orgSlug && projectSlug && isPlaywrightTrace) ? api.run.playwrightTrace.GET.prepare({
        attachmentId,
        orgSlug,
        projectSlug,
        reportId,
      }).url.toString() 
      : localApi!.report.attachment.GET.prepare({
        attachmentId,
      }).url.toString();
    return html`
      <sl-divider style="flex: auto;"></sl-divider>
      <h-box>
        <teststats-title .project=${this._project} .testStats=${testStats}></teststats-title>
        <x-filler></x-filler>
        <history-snippet size=large
          @fk-select=${commit ? (event: CustomEvent<{ day: Date, outcome: WireTypes.Outcome }>) => {
            this._executionHistory?.show({
              testStats,
              since: timeDay.floor(event.detail.day),
              until: timeDay.offset(timeDay.floor(event.detail.day), 1),
              timeline: Timeline.deserialize(testStats.timeline),
              head: { type: 'commit', name: commit.commitId, commit: commit },
            });
          } : nothing}
          .selectedIndex=${testStats.daily.length - 1}
          .outcomes=${testStats.daily.map(d => d.outcome).toReversed()}
          .days=${history?.historyDays.toReversed()}
        ></history-snippet>
      </h-box>

      <test-run
        .attachmentURLBuilder=${attachmentURLBuilder}
        .report=${report}
        .attempts=${this._testRunAttemptsTask.value}
        .isRegression=${testStats.outcome === 'regressed'}
        .attempt=${this._urlState.testAttempt}
        @test-run-attempt-selected=${(event: CustomEvent<{ attemptIdx: number }>) => {
          this._urlState.testAttempt = event.detail.attemptIdx;
        }}
      ></test-run>
    `;
  }

  static styles = [linkStyles, pageStyles, css`
    :host {
      display: flex;
      width: 100vw;
      height: 100vh;
      flex-direction: column;
    }

    .title {
      font-size: var(--sl-font-size-2x-large);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    test-run {
      margin-top: var(--sl-spacing-x-large);
    }

    .details {
      font-size: var(--sl-font-size-small);
      color: var(--sl-color-neutral-500);
      display: flex;
      align-items: center;
      gap: var(--sl-spacing-small);
    }

    sl-skeleton {
      --border-radius: var(--sl-border-radius-medium);
    }
  `]
}
