import { css, html, LitElement } from 'lit';
import { customElement } from 'lit/decorators.js';
import './generated/componentImports.js';
import './generated/shoelaceImports.js';

import { registerIconLibrary } from '@shoelace-style/shoelace/dist/utilities/icon-library.js';
import { api } from './api.js';
import { contexts, ContextTask, provide } from './contexts.js';
import { PageAdministrationBilling } from './page-administration-billing.js';
import { PageAdministrationQueues } from './page-administration-queues.js';
import { PageCLI } from './page-cli.js';
import { PageNewOrganization } from './page-new-organization.js';
import { PageNewProject } from './page-new-project.js';
import { PageOrganizationBilling } from './page-organization-billing.js';
import { PageOrganizationMembers } from './page-organization-members.js';
import { PageOrganizationSettings } from './page-organization-settings.js';
import { PageOrganizationUsage } from './page-organization-usage.js';
import { PageOrganization } from './page-organization.js';
import { PageProjectCollaborators } from './page-project-collaborators.js';
import { PageProjectHistory } from './page-project-history.js';
import { PageProjectPulls } from './page-project-pulls.js';
import { PageProjectRuns } from './page-project-runs.js';
import { PageProjectSettings } from './page-project-settings.js';
import { PageProjectSuperadm } from './page-project-superadm.js';
import { PageProjectUsage } from './page-project-usage.js';
import { PageReport } from './page-report.js';
import { PageRun } from './page-run.js';
import { PageUserSettings } from './page-user-settings.js';
import { PageUser } from './page-user.js';
import { Router } from './router.js';

registerIconLibrary('boxicons', {
  resolver: name => {
    let folder = 'regular';
    if (name.substring(0, 4) === 'bxs-') folder = 'solid';
    if (name.substring(0, 4) === 'bxl-') folder = 'logos';
    return `/assets/boxicons/${folder}/${name}.svg`;
  },
  mutator: svg => svg.setAttribute('fill', 'currentColor')
});

@customElement('the-app')
export class App extends LitElement {

  private _userTask = new ContextTask(this, contexts.user, {
    args: () => [],
    task: async ([], { signal }) => {
      return await api.user.whoami.GET(undefined, { signal }).catch(e => undefined);
    }
  });

  private _serverInfoTask = new ContextTask(this, contexts.serverInfo, {
    args: () => [],
    task: async ([], { signal }) => {
      const result = await api.serverInfo.GET(undefined, { signal }).catch(e => undefined);
      console.log(result);
      return result;
    }
  });

  @provide({ context: contexts.router })
  private _router: Router = new Router();

  constructor() {
    super();
    this._router.ignoreClientSideNavigation('/login/*');
    this._router.ignoreClientSideNavigation('/docs/*');
    this._router.ignoreClientSideNavigation('/logout/*');
    this._router.ignoreClientSideNavigation('/*/billing/checkout');
    this._router.ignoreClientSideNavigation('/api/run/playwrightTrace/*');
    this._router.setNotFoundRenderer(() => html`<page-http-error code=404></page-http-error>`);

    const loggedInUser = this._router.withMiddleware(() => {
      if (!this._userTask.value) {
        return html`<page-login></page-login>`;
      }
    });

    const anonUser = this._router.withMiddleware(() => {
      return undefined;
    });

    loggedInUser.allRoutes(PageAdministrationBilling.routes());
    loggedInUser.allRoutes(PageAdministrationQueues.routes());
    loggedInUser.allRoutes(PageOrganizationSettings.routes());
    loggedInUser.allRoutes(PageOrganizationUsage.routes());
    loggedInUser.allRoutes(PageOrganizationMembers.routes());
    loggedInUser.allRoutes(PageOrganizationBilling.routes());
    loggedInUser.allRoutes(PageNewProject.routes());
    loggedInUser.allRoutes(PageNewOrganization.routes());
    loggedInUser.allRoutes(PageUser.routes());
    loggedInUser.allRoutes(PageUserSettings.routes());
    loggedInUser.allRoutes(PageCLI.routes());
    loggedInUser.allRoutes(PageProjectSuperadm.routes());
    anonUser.allRoutes(PageReport.routes());
    anonUser.allRoutes(PageProjectSettings.routes());
    anonUser.allRoutes(PageProjectRuns.routes());
    anonUser.allRoutes(PageProjectPulls.routes());
    anonUser.allRoutes(PageProjectHistory.routes());
    anonUser.allRoutes(PageProjectCollaborators.routes());
    anonUser.allRoutes(PageProjectUsage.routes());
    anonUser.allRoutes(PageRun.routes());
    anonUser.allRoutes(PageOrganization.routes());
  }

  render() {
    return this._userTask.render({
      pending: () => html``,
      error: () => html`Failed to fetch user`,
      complete: () => this._router,
    })
  }

  static styles = [css`
    :host {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }
  `];
}
