import { ContextType, consume } from '@lit/context';
import { LitElement, css, html } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { pageStyles } from './components/cssstyles.js';
import { contexts } from './contexts.js';
import { resetcss } from './resetcss.js';

@customElement('page-login')
export class PageLogin extends LitElement {
  static page() {
    return html`<page-login></page-login>`;
  }

  @property() redirect?: string;
  @consume({ context: contexts.serverInfo, subscribe: true }) private _serverInfo?: ContextType<typeof contexts.serverInfo>;

  override render() {
    return html`
      <app-header .customTitle=${""}></app-header>

      <v-box style="flex: 2; align-items: center; justify-content: center;">
        <section class="card">
          <div class="title">
            <img src="./logo.svg" class="logo">
            <span>Sign in to flakiness.io</span>
          </div>
          <div class=info>Use your GitHub account to access your projects and reports</div>
          <sl-button
            variant=primary
            size=large
            href=${`/login/github?return_to=${encodeURIComponent(window.location.href)}`}
          ><sl-icon slot=prefix name=github></sl-icon>Sign in with GitHub</sl-button>
        </section>
      </v-box>
      <app-footer style="flex: 1;"></app-footer>
    `
  }

  static styles = [resetcss, pageStyles, css`
    :host {
      background-color: var(--fk-color-canvas);
    }
    sl-button {
    }

    h-box {
      align-items: center;
      justify-content: center;
    }

    .card {
      box-shadow: var(--sl-shadow-x-large);
      gap: var(--sl-spacing-x-large);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: white;
      border: 1px solid var(--sl-color-neutral-200);
      border-radius: 24px;
      padding: var(--sl-spacing-2x-large);

      .info {
        color: var(--sl-color-neutral-500);
      }

      .title {
        font-size: var(--sl-font-size-x-large);
        font-family: "Raleway", sans-serif;
        font-optical-sizing: auto;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        font-style: normal;
        gap: 10px;

        .logo {
          width: 1.25em;
        }
      }
    }
  `];
}
