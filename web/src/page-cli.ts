import { Task } from '@lit/task';
import { LitElement, css, html, nothing } from 'lit';
import { customElement, queryAll, state } from 'lit/decorators.js';
import { api } from './api.js';

import { clientName } from '@flakiness/server/common/knownClientIds.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { ContextType } from '@lit/context';
import { linkStyles, pageStyles } from './components/cssstyles.js';
import { consume, contexts } from './contexts.js';
import { RouteConfig, URLState } from './router.js';
import { assert, consumeDOMEvent } from './utils.js';

const pageState = new URLState({
  code: URLState.option<string>({
    name: 'code',
    default: '',
    encode: code => code ? code.slice(0, 4) + '-' + code.slice(4) : '',
    decode: code => code ? code.replaceAll('-', '') : code,
  }),
});

@customElement('page-cli')
export class PageCLI extends LitElement {
  static routes(): RouteConfig[] {
    return [{
      path: [ '/cli', '/cli/' ],
      render: (groups) => html`<page-cli></page-cli>`,
    }];
  }

  static url(): string {
    return new URL('/cli', window.location.href).href;
  }

  @consume({ context: contexts.user, subscribe: true })
  private _user?: ContextType<typeof contexts.user>;

  @consume({ context: contexts.serverInfo, subscribe: true })
  private _serverInfo?: ContextType<typeof contexts.serverInfo>;
  private _urlState = pageState.bind(this);

  @state() private _status?: 'approved'|'rejected';

  private _requestTask = new Task(this, {
    args: () => [this._urlState.code, this._user] as const,
    task: async ([userCode, user], { signal }) => {
      assert(userCode && user && userCode.length === 8);
      return await api.deviceauth.getRequest.GET({ userCode });
    }
  });

  @queryAll('input.char') private _charInputs?: HTMLInputElement[];

  private _onInput(e: InputEvent) {
    if (!e.target || !this._charInputs || !(e.target instanceof HTMLInputElement))
      return;
    this._fillInput(e.target, e.data ?? undefined);
  }

  private _fillInput(input: HTMLInputElement, value?: string) {
    if (!this._charInputs)
      return;
    const allInputs = Array.from(this._charInputs);
    const inputIndex = allInputs.indexOf(input);
    if (!value) {
      input.value = '';
      const prevInput = allInputs[inputIndex - 1];
      this._urlState.code = allInputs.map(i => i.value).join('');

      prevInput?.focus();
      prevInput?.select();
    } else {
      for (let i = 0; i < value.length && i + inputIndex < allInputs.length; ++i)
        allInputs[i + inputIndex].value = value[i].toUpperCase();
      this._urlState.code = allInputs.map(i => i.value).join('');

      const nextInput = allInputs.at(inputIndex + value.length) ?? allInputs.at(-1);
      nextInput?.focus();
      nextInput?.select();
    }
  }

  private _onFocus(e: FocusEvent) {
    if (!e.target || !(e.target instanceof HTMLInputElement) || !this._charInputs)
      return;
    const t: HTMLInputElement = e.target;
    const allInputs = Array.from(this._charInputs);
    let index = allInputs.indexOf(t);
    const toFocus = Math.min(this._urlState.code.length, index);
    if (toFocus === index)
      t.select();
    else
      allInputs[toFocus]?.focus();
  }

  private _onKeyDown(e: KeyboardEvent) {
    if (!e.target || !(e.target instanceof HTMLInputElement) || !this._charInputs)
      return;
    const allInputs = Array.from(this._charInputs);
    const inputIndex = allInputs.indexOf(e.target);
    if (e.key === 'ArrowLeft') {
      allInputs[inputIndex - 1]?.focus();
      consumeDOMEvent(e);
    } else if (e.key === 'ArrowRight') {
      allInputs[inputIndex + 1]?.focus();
      consumeDOMEvent(e);
    } else if (e.key === 'Backspace') {
      this._fillInput(e.target, undefined);
      consumeDOMEvent(e);
    } else {
      e.target.select();
    }
  }

  private _onPaste(e: ClipboardEvent) {
    e.preventDefault();
    const pasted = e.clipboardData?.getData('text').trim().replaceAll('-', '');
    if (!e.target || !(e.target instanceof HTMLInputElement) || !pasted)
      return;
    this._fillInput(e.target, pasted);
  }

  render() {
    const request = this._requestTask.value;

    return html`
      <app-header .customTitle=${'CLI Application Authentication'}></app-header>
      <app-body>
        <h1>User Code</h1>
        ${!this._user ? this._renderLogin() :
          !request ? this._renderUserCode() :
          this._renderRequest(request)
        }
      </app-body>
      <app-footer></app-footer>
    `;
  }

  private async _onApprove(deviceCode: string) {
    await api.deviceauth.approveRequest.POST({ deviceCode });
    this._status = 'approved';
  }

  private async _onReject(deviceCode: string) {
    await api.deviceauth.rejectRequest.POST({ deviceCode });
    this._status = 'rejected';
  }

  private _renderUserCode() {
    const code = this._urlState.code;
    return html`
      <section class=code
        @paste=${this._onPaste}
        @keydown=${this._onKeyDown} 
        @input=${this._onInput}
      >
        ${Array(8).fill(0).map((_, idx) => html`
          <input @focus=${this._onFocus} ?autofocus=${idx === code.length} class=char maxlength=1 value=${code[idx]}>
        `)}
        <span>-</span>
      </section>
      ${code.length === 8 ? html`
        <div>No authentication request found for this code. Please verify the code and try again.</div>
      ` : nothing}
    `;
  }

  private _renderRequest(request: WireTypes.DeviceAuthRequest) {
    if (this._status === 'approved') {
      return html`
        <fk-callout variant=tip>
          Device "${request.deviceName}" has been successfully approved! You can now close this page.
        </fk-callout>
      `;
    }
    if (this._status === 'rejected') {
      return html`
        <fk-callout variant=warn>
          Device "${request.deviceName}" has been rejected and will not have access. You can now close this page.
        </fk-callout>
      `;
    }

    return html`
      <div>The application <strong>${clientName(request.clientId)}</strong> on device <strong>"${request.deviceName}"</strong> is requesting permission to act on your behalf. Do you want to allow this?</div>
      <h-box>
        <sl-button @click=${() => this._onReject(request.deviceCode)}>Reject</sl-button>
        <sl-button variant=primary @click=${() => this._onApprove(request.deviceCode)}>Approve</sl-button>
      </h-box>
    `;
  }

  private _renderLogin() {
    return html`
      <div>Please log in to authenticate CLI applications.</div>
      <sl-button href='/login/github?return_to=${encodeURIComponent(window.location.href)}' variant=primary>Login</sl-button>
    `;
  }

  static styles = [linkStyles, pageStyles, css`
    .code {
      display: grid;
      grid-template-columns: repeat(9, auto);
      align-items: center;
      justify-content: center;
      gap: var(--sl-spacing-small);

      --font-size: var(--sl-font-size-4x-large);

      span {
        font-size: var(--font-size);
        grid-row: 1;
        grid-column: 5;
      }

      input {
        font-size: var(--font-size);
        width: 1em;
        height: 1em;
        text-align: center;
        caret-color: transparent;

        &::selection {
          background: transparent;
        }
      }

      input:focus {
        background: yellow;
      }
    }

    .sessions {
      display: grid;
      grid-template-columns: auto 1fr auto;
      border: 1px solid var(--fk-color-border);
      border-radius: var(--sl-border-radius-large);
    }

    .session {
      display: grid;
      grid-column: 1/-1;
      grid-template-columns: subgrid;
      padding: var(--sl-spacing-small);
      gap: var(--sl-spacing-small);
      align-items: center;

      &.header {
        font-weight: bold;
        background: var(--sl-color-neutral-50);
      }

      &:not(.header):hover {
        background: var(--fk-color-tablerow-hover);
      }
    }
  `];
}
