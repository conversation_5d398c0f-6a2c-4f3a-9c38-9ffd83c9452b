import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { html, TemplateResult } from 'lit';
import { api } from '../api.js';
import { DataColumn, DataTableProvider } from '../components/data-table.js';


export const columns = {
  name: {
    sortAxis: undefined,
    width: '1fr',
    renderHeader() { return html`Message`; },
    renderElement(pr): TemplateResult {
      return html`<p>${pr.title}</p>`;
    },
  },
} satisfies Record<string, DataColumn<WireTypes.PullRequest, WireTypes.PullRequestSortAxis>>;

const DEFAULT_SORT_OPTIONS = {
  axis: 'created' as WireTypes.PullRequestSortAxis,
  direction: 'desc' as WireTypes.SortDirection,
}

export function createPullsDataProvider(options: {
  orgSlug: string,
  projectSlug: string,
  state: WireTypes.PullRequestState,
}): DataTableProvider<WireTypes.PullRequest, WireTypes.PullRequestSortAxis> {
  return {
    loader: (pageOptions, sortOptions, signal) => api.project.pulls.GET({
      ...options,
      sortOptions: sortOptions ?? DEFAULT_SORT_OPTIONS,
      pageOptions,
    }),
    columns: [columns.name],
    defaultSortColumn: columns.name,
    noDataMessage: 'No Pull Requests',
  };
}
