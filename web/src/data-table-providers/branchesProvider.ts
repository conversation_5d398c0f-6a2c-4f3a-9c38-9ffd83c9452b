import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { html, TemplateResult } from 'lit';
import { api } from '../api.js';
import { DataColumn, DataTableProvider } from '../components/data-table.js';

const COLUMN_REF_NAME: DataColumn<WireTypes.Ref, ''> = {
  sortAxis: undefined,
  width: 'auto',
  renderHeader() { return html`Name`; },
  renderElement(ref): TemplateResult {
    return html`${ref.name}`;
  },
};

const COLUMN_REF_DATE: DataColumn<WireTypes.Ref, ''> = {
  sortAxis: undefined,
  width: 'auto',
  renderHeader() { return html`Date`; },
  renderElement(ref): TemplateResult {
    return html`<sl-format-date month="long" day="numeric" year="numeric" hour="numeric" minute="numeric" date=${new Date(ref.commit.timestamp).toISOString()}></sl-format-date>`;
  },
};

const COLUMN_REF_COMMIT: DataColumn<WireTypes.Ref, ''> = {
  sortAxis: undefined,
  width: 'auto',
  renderHeader() { return html`Commit`; },
  renderElement(ref): TemplateResult {
    return html`<fk-shortcommit .commit=${ref.commit}></fk-shortcommit>`;
  },
};

export function createGitRefsDataProvider(options: {
  orgSlug: string,
  projectSlug: string,
  filter: string,
}): DataTableProvider<WireTypes.Ref, ''> {
  return {
    columns: [
      COLUMN_REF_NAME,
      COLUMN_REF_DATE,
      COLUMN_REF_COMMIT,
    ],
    noDataMessage: 'No Branches',
    defaultSortColumn: undefined,
    loader: async (pageOptions, sortOptions, signal) => {
      return await api.project.filterBranches.GET({
        ...options,
        pageOptions: pageOptions,
      }, { signal });
    }
  }
}
