
import { ContextType } from '@lit/context';
import { Task } from '@lit/task';
import { LitElement, css, html } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { linkStyles, pageStyles } from './components/cssstyles.js';
import { ProjectAlerts } from './components/project-alerts.js';
import { ProjectContext } from './components/project-context.js';
import { consume, contexts } from './contexts.js';
import { createPullsDataProvider } from './data-table-providers/pullsProvider.js';
import { markdownStyles } from './markdown.js';
import { RouteConfig } from './router.js';
import { assert } from './utils.js';

@customElement('page-project-pulls')
export class PageProjectPulls extends LitElement {
  static url(options: { orgSlug: string, projectSlug: string, }): string {
    return new URL(['', options.orgSlug, options.projectSlug, 'pulls'].join('/'), window.location.href).href;
  }

  static routes(): RouteConfig[] {
    return [{
      path: [
        '/:org/:project/pulls',
        '/:org/:project/pulls/'
      ],
      render: (groups) => ProjectContext.render({
        orgSlug: groups?.org,
        projectSlug: groups?.project,
      }, html`
        <page-project-pulls
          .orgSlug=${groups?.org}
          .projectSlug=${groups?.project}
        ></page-project-pulls>
      `),
    }];
  }

  @consume({ context: contexts.user, subscribe: true }) private _user?: ContextType<typeof contexts.user>;
  @consume({ context: contexts.router, subscribe: true }) private _router?: ContextType<typeof contexts.router>;
  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;

  @property({ attribute: false }) projectSlug?: string;
  @property({ attribute: false }) orgSlug?: string;

  private _dataTableProviderTask = new Task(this, {
    args: () => [
      this._project
    ] as const,
    task: async ([project], { signal }) => {
      assert(project);
      return createPullsDataProvider({
        orgSlug: project.org.orgSlug,
        projectSlug: project.projectSlug,
        state: 'open',
      });
    }
  });

  override render() {
    return html`
      <project-header
        .orgSlug=${this.orgSlug}
        .projectSlug=${this.projectSlug}
        .submenu=${"pulls"}
      ></project-header>
      <app-body wide>
        ${ProjectAlerts.maybeRender(this._project)}
        <data-table
          .pageNumber=${0}
          .pageSize=${20}
          .provider=${this._dataTableProviderTask.value}
        ></data-table>
      </app-body>
      <app-footer></app-footer>
    `;
  }

  static styles = [...markdownStyles, pageStyles, linkStyles, css`
    .markdown-body {
      margin-top: var(--sl-spacing-medium);
    }

    .commit-history {
      fk-commitstats + fk-commitstats {
        border-top: 1px solid var(--fk-color-border);
        border-radius: 0px;
      }
    }
  `]
}
