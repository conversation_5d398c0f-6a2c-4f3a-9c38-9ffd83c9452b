import { consume, ContextType } from '@lit/context';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { contexts } from '../contexts.js';
import { PageOrganizationBilling } from '../page-organization-billing.js';
import { PageOrganizationMembers } from '../page-organization-members.js';
import { PageOrganizationSettings } from '../page-organization-settings.js';
import { PageOrganizationUsage } from '../page-organization-usage.js';
import { PageOrganization } from '../page-organization.js';
import { linkStyles } from './cssstyles.js';

@customElement('org-header')
class OrgHeader extends LitElement {
  @consume({ context: contexts.org, subscribe: true }) @state() private _org?: ContextType<typeof contexts.org>;
  @property({ }) submenu: string = 'projects';
  
  @property({ attribute: false }) orgSlug?: string;
  @consume({ context: contexts.serverInfo, subscribe: true }) @state() private _serverInfo?: ContextType<typeof contexts.serverInfo>;
  @consume({ context: contexts.orgMembers, subscribe: true }) @state() private _orgMembers?: ContextType<typeof contexts.orgMembers>;

  override render() {
    return html`
      <app-header>
        <sl-breadcrumb>
          <span slot="separator">/</span>
          <sl-breadcrumb-item href=${this.orgSlug ? PageOrganization.url({ orgSlug: this.orgSlug }) : nothing }><b>${this._org?.orgName ?? nothing}</b></sl-breadcrumb-item>
        </sl-breadcrumb>

        ${this.orgSlug ? html`
          <app-header-submenu
            href=${PageOrganization.url({ orgSlug: this.orgSlug })}
            ?active=${this.submenu === 'projects'}
          ><sl-icon src='/repo.svg'></sl-icon>Projects</app-header-submenu>

          ${this._org && this._org.access ? html`
            <app-header-submenu
              href=${PageOrganizationMembers.url({ orgSlug: this.orgSlug })}
              ?active=${this.submenu === 'members'}
            ><sl-icon name='people'></sl-icon>Members
              ${
                this._orgMembers === undefined ? html`<sl-skeleton effect=pulse style="width: 2ch"></sl-skeleton>` :
                html`<sl-tag size=small pill>${this._orgMembers.members.length + 1}</sl-tag>`
              }
            </app-header-submenu>
            <app-header-submenu
              href=${PageOrganizationUsage.url({ orgSlug: this.orgSlug })}
              ?active=${this.submenu === 'usage'}
            ><sl-icon name='speedometer2'></sl-icon>Usage</app-header-submenu>
            ${this._serverInfo?.enabledBilling ? html`
              <app-header-submenu
                href=${PageOrganizationBilling.url({ orgSlug: this.orgSlug })}
                ?active=${this.submenu === 'billing'}
              ><sl-icon name='credit-card'></sl-icon>Billing<icon-org-issues .org=${this._org}></icon-org-issues></app-header-submenu>
            ` : nothing}
            <app-header-submenu
              href=${PageOrganizationSettings.url({ orgSlug: this.orgSlug })}
              ?active=${this.submenu === 'settings'}
            ><sl-icon name=gear></sl-icon>Settings</app-header-submenu>
          ` : nothing}
        ` : nothing}
      </app-header>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      display: block;
    }

    sl-breadcrumb-item::part(base) {
      font-size: var(--sl-font-size-medium);
      font-weight: var(--sl-font-weight-normal);
    }

    sl-tag::part(base) {
      height: 16px;
    }

    sl-breadcrumb-item::part(label) {
      color: var(--sl-color-neutral-800);
    }

    .first-line {
      gap: var(--sl-spacing-medium);
      flex-grow: 1;
    }
  `];
}
