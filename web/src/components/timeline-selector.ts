import { FlakinessReport } from '@flakiness/report';
import { TimelineKey } from '@flakiness/server/common/timeline/timeline.js';
import { TimelineSplit } from '@flakiness/server/common/timeline/timelineSplit.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { Multimap } from '@flakiness/shared/common/multimap.js';
import { consume, ContextType } from '@lit/context';
import { SlDialog, SlDropdown, SlHideEvent, SlSelectEvent } from '@shoelace-style/shoelace';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, query } from 'lit/decorators.js';
import { contexts } from '../contexts.js';
import { linkStyles } from './cssstyles.js';
import { ParametersTable } from './parameters-table.js';

/**
 * TimelineSelector is actually a "split" selector.
 * It renders a bunch of splits, based on the given envs, and also allows to create a custom
 * timeline split.
 */

const ICON_CUSTOM_TIMELINE = html`<sl-icon name=sliders slot=prefix></sl-icon>`;

@customElement('timeline-selector')
class TimelineSelector extends LitElement {
  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;

  @property({ attribute: false }) split?: TimelineSplit;
  @property({ attribute: false }) envs?: WireTypes.RunEnvironment[];

  @property({ type: Boolean }) multiple?: boolean;

  @query('parameters-table') _params?: ParametersTable;
  @query('sl-dropdown') _dropdown?: SlDropdown;
  @query('sl-dialog') _drawer?: SlDialog;

  private _renderCategoryMenuItem(options: {
    categoryName: string,
    categorySplit: TimelineSplit,
  }) {
    const envs = (this.envs ?? []);//.filter(env => options.categorySplit.acceptsEnvironment(env));
    const timelines = options.categorySplit.timelines(envs).sort((t1, t2) => t1.compare(t2));
    if (!timelines.length)
      return nothing;
    return html`
      <sl-menu-item>
        ${this._renderSplitIcon(options.categorySplit)}
        ${options.categoryName}
        <sl-menu slot=submenu>
          ${this.multiple ? html`
            <sl-menu-item .split=${options.categorySplit} type=checkbox ?checked=${this.split?.isEqual(options.categorySplit)}>
              ${this._renderSplit(options.categorySplit)}
            </sl-menu-item>
            <sl-divider></sl-divider>
          ` : nothing}
          ${timelines.map(t => TimelineSplit.fromTimeline(t)).map(split => html`
            <sl-menu-item .split=${split} type=checkbox ?checked=${this.split?.isEqual(split)}>
              ${this._renderSplit(split)}
            </sl-menu-item>
          `)}
        </sl-menu>
      </sl-menu-item>
    `;
  }

  render() {
    if (!this.split || !this.envs)
      return nothing;

    const categories = new Multimap<string, WireTypes.RunEnvironment>();
    for (const e of this.envs)
      categories.set(e.category, e);
    categories.deleteAll(FlakinessReport.CATEGORY_PLAYWRIGHT);
    categories.deleteAll(FlakinessReport.CATEGORY_JUNIT);
    categories.deleteAll(FlakinessReport.CATEGORY_PERF);

    return html`
      <sl-dialog style="--size: 50vw;" label='Timeline Editor' @sl-show=${() => {
        if (this._params)
          this._params.split = this.split;
      }}>
        <v-box style="">
          <parameters-table
            .split=${this.split}
            .envs=${this.envs}
            .multiple=${this.multiple}
          ></parameters-table>
        </v-box>
        <sl-button slot="footer" variant="primary" @click=${(event: SlHideEvent) => {
          this.split = this._params?.split;
          this.dispatchEvent(new CustomEvent<TimelineSplit>('fk-select', { detail: this.split }));
          this._drawer?.hide();
        }}>Accept</sl-button>
      </sl-dialog>
      
      <sl-dropdown @sl-select=${(event: SlSelectEvent) => {
        const item = event.detail.item;
        if ((item as any).openGranularTimeline) {
          this._drawer?.show();
        } else if ((item as any).split) {
          this.split = (item as any).split;
          this.dispatchEvent(new CustomEvent<TimelineSplit>('fk-select', { detail: this.split }));
        }
      }}>
        <sl-button slot=trigger caret>
          ${this._renderSplitIcon(this.split)}
          ${this._renderSplit(this.split)}
        </sl-button>
        <sl-menu>
          ${this.multiple ? html`
            <sl-menu-item .split=${TimelineSplit.DEFAULT} type=checkbox ?checked=${this.split?.isEqual(TimelineSplit.DEFAULT)}>
              ${this._renderSplit(TimelineSplit.DEFAULT)}
            </sl-menu-item>
            <sl-divider></sl-divider>
          ` : nothing}
          ${this._renderCategoryMenuItem({
            categoryName: 'Playwright',
            categorySplit: TimelineSplit.PW_PROJECTS,
          })}
          ${this._renderCategoryMenuItem({
            categoryName: 'JUnit Environments',
            categorySplit: TimelineSplit.JUNIT_ENVS,
          })}
          ${this._renderCategoryMenuItem({
            categoryName: 'Performance Benchmarks',
            categorySplit: TimelineSplit.PERF_BENCHMARKS,
          })}
          ${categories.map((category) => this._renderCategoryMenuItem({
            categoryName: category,
            categorySplit: TimelineSplit.DEFAULT
              .toggleValue(TimelineKey.systemKeys.CATEGORY, category)
              .splitBy(TimelineKey.systemKeys.NAME)
              .splitBy(TimelineKey.systemKeys.CONFIG_PATH)
          }))}
          <sl-menu-item .openGranularTimeline=${true}>
            ${ICON_CUSTOM_TIMELINE}
            Custom...
          </sl-menu-item>
        </sl-menu>
      </sl-dropdown>
    `;
  }

  private _renderSplitIcon(split: TimelineSplit) {
    if (split.isEqual(TimelineSplit.DEFAULT))
      return html`<sl-icon name='list-nested' slot=prefix></sl-icon>`;;

    const categories = split.categories();
    if (categories.length !== 1)
      return ICON_CUSTOM_TIMELINE;
    const cat = categories[0];
    if (cat === 'pytest')
      return html`<sl-icon src="/python.svg" slot=prefix></sl-icon>`;

    if (cat === FlakinessReport.CATEGORY_JUNIT)
      return html`<sl-icon name='filetype-xml' slot=prefix></sl-icon>`;
    if (cat === FlakinessReport.CATEGORY_PERF)
      return html`<sl-icon name='speedometer' slot=prefix></sl-icon>`;
    if (cat === FlakinessReport.CATEGORY_PLAYWRIGHT)
      return html`<sl-icon src='/masks.svg' slot=prefix></sl-icon>`;
    return html`<sl-icon slot=prefix></sl-icon>`;
  }

  private _renderSplit(split: TimelineSplit) {
    if (split.isEqual(TimelineSplit.DEFAULT)) {
      return html`All Timelines`;
    }

    if (split.isEqual(TimelineSplit.PW_PROJECTS)) {
      return html`All Playwright Projects`;
    }

    if (split.isEqual(TimelineSplit.PERF_BENCHMARKS)) {
      return html`All Performance Benchmarks`;
    }

    if (split.isEqual(TimelineSplit.JUNIT_ENVS)) {
      return html`All JUnit Environments`;
    }

    const categories = split.categories();
    if (categories.length === 1 && split.isEqual(TimelineSplit.categorySplit(categories[0]))) {
      return html`All ${categories[0]} timelines`;
    }

    const timelines = split.timelines(this.envs ?? []);
    if (timelines.length === 1) {
      const description = timelines[0].description();
      return html`
        ${description.system ? `[${description.system}]` : nothing}
        ${description.name ?? description.configPath}
        ${description.metadata ? `(${description.metadata})` : nothing}
        ${description.configPath ? html`<span class=timeline-path>– ${description.configPath}</span>` : nothing}
      `;
    }

    return html`Custom ${timelines.length} timelines`;
  }

  static styles = [linkStyles, css`
    timeline-selector-view {
      background-color: white;
    }

    .timeline-path {
      color: var(--sl-color-neutral-500);
    }

    sl-dialog {
      --width: 75vw;
    }
  `];
}
