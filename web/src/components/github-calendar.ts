import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { max, min, timeDay, timeFormat } from 'd3';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { styleMap } from 'lit/directives/style-map.js';
import { linkStyles } from './cssstyles.js';

export type DayStatus = WireTypes.DayOutcome|'loading';

export type DayData = {
  date: Date,
  status: DayStatus,
}

type CalendarDay = {
  dayOfMonth: number, // 1-31
  dayOfWeek: number, // 0-6
  relativeWeek: number, // 0-.. US weeks in the selected interval, starting with the 0 as the first week in the interval.
  since: Date, // Including
  until: Date, // Excluding
  status: DayStatus,
}
export type CalendarViewProvider = (days: CalendarDay[], signal: AbortSignal) => Promise<DayStatus[]>;

const getMonthAbbr = timeFormat('%b');
const formatDate = timeFormat('%b %-d, %Y');

// This should render a calendar month with days highlighted with outcomes.
// When a day is clicked, an event should be emitted.
@customElement('github-calendar')
class GithubCalendar extends LitElement {
  @property({ attribute: false }) selectedSince?: Date;
  @property({ attribute: false }) selectedUntil?: Date;
  @property({ attribute: false }) data?: DayData[];

  @state() private _hoveredDay?: Date;

  private _onMouseEnter(since: Date, e: MouseEvent) {
    this._hoveredDay = since;
  }

  private _onMouseLeave() {
    this._hoveredDay = undefined;
  }

  private _isSelected(day: CalendarDay) {
    if (!this.selectedSince || !this.selectedUntil)
      return false;
    return this.selectedSince <= day.since && day.since < this.selectedUntil;
  }

  private _isLastSelected(day: CalendarDay) {
    if (!this.selectedSince || !this.selectedUntil)
      return false;
    return timeDay.offset(this.selectedUntil, -1) <= day.since && day.since < this.selectedUntil;
  }

  render() {
    if (!this.data)
      return nothing;
    const minDate = min(this.data?.map(d => d.date));
    const maxDate = max(this.data.map(d => d.date));

    if (!minDate || !maxDate)
      return nothing;

    const dayToStatus = new Map(this.data.map(({ date, status }) => [+timeDay.floor(date), status]));
    const since = timeDay.floor(minDate);
    const until = timeDay.offset(timeDay.floor(maxDate), 1);
    let weekCounter = 0;
    const days: CalendarDay[] = timeDay.range(since, until).map(d => ({
      dayOfMonth: d.getDate(),
      dayOfWeek: d.getDay(),
      relativeWeek: d.getDay() === 6 ? weekCounter++ : weekCounter,
      since: d,
      until: timeDay.offset(d, 1),
      status: dayToStatus.get(+d) ?? 'idle',
    }));

    const sundays = days.filter(day => day.dayOfWeek === 0);
    const allMonthLabels = sundays.filter((day, index) => index === 0 || sundays[index].dayOfMonth < sundays[index - 1].dayOfMonth);
    const monthLabels = allMonthLabels.filter((day, index) => {
      // We always take the last month.
      if (index === allMonthLabels.length - 1)
        return true;
      const next = allMonthLabels[index + 1];
      return next.relativeWeek - day.relativeWeek > 1;
    });
    const lastWeek = Math.max(...days.map(day => day.relativeWeek));
    return html`
      <section class=days style=${styleMap({
        '--weeks': lastWeek + 1,
      })}>
        <h-box style="gap: 0; grid-row: 1; grid-column: 1;">
          <sl-icon-button name=chevron-left @click=${() => {
            this.dispatchEvent(new CustomEvent<{ since: Date, until: Date }>('weekchanged', {
              detail: {
                since: timeDay.offset(since, -28),
                until: timeDay.offset(until, -28),
              }
            }));
          }}></sl-icon-button>
        </h-box>
        <sl-icon-button name=chevron-right style="grid-row: 1; grid-column: ${lastWeek + 3};"  @click=${() => {
          this.dispatchEvent(new CustomEvent<{ since: Date, until: Date }>('weekchanged', {
            detail: {
              since: timeDay.offset(since, 28),
              until: timeDay.offset(until, 28),
            },
          }));
        }}></sl-icon-button>
        <span class=day-of-week style="grid-row: 3; grid-column: 1;">Mon</span>
        <span class=day-of-week style="grid-row: 5; grid-column: 1;">Wed</span>
        <span class=day-of-week style="grid-row: 7; grid-column: 1;">Fri</span>

        ${this._hoveredDay ? html`
          <span class=month-of-year style=${styleMap({
            'grid-column-start': 2,
            'grid-column-end': lastWeek + 2,
            'align-self': 'center',
            'justify-self': 'center',
            'width': 'auto',
            'font-size': 'var(--sl-font-size-medium)',
            gridRow: 1,
          })}>${formatDate(this._hoveredDay)}</span>
        ` : monthLabels.map(({ relativeWeek, since }) => html`
          <span class=month-of-year style=${styleMap({
            gridColumn: relativeWeek + 2,
            'align-self': 'center',
            gridRow: 1,
          })}>${getMonthAbbr(since)}</span>
        `)}
        <div style=${styleMap({
          'grid-column': '-2',
          'grid-row': '2/-1',
        })}>
          <slot name=widgets></slot>
        </div>
        <section style=${styleMap({
          'display': 'grid',
          'grid-template-rows': 'subgrid',
          'grid-template-columns': 'subgrid',
          'grid-column': '2/-2',
          'grid-row': '2/-1',
        })} @mouseleave=${this._onMouseLeave}>
          ${days.map(day => html`
            <day-outcome
              size=large
              ?selected=${this._isSelected(day)}
              ?not-last-selected=${this._isSelected(day) && !this._isLastSelected(day)}
              .outcome=${day.status === 'loading' ? undefined : day.status}
              style=${styleMap({
                gridColumn: day.relativeWeek + 1,
                gridRow: day.dayOfWeek + 1,
              })}
              @click=${(event: MouseEvent) => {
                this.dispatchEvent(new CustomEvent<Date>('dayselected', {
                  detail: day.since,
                }));
              }}
              @mouseenter=${this._onMouseEnter.bind(this, day.since)}
            ></day-outcome>
          </section>
        `)}
      </section>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      display: flex;
      align-items: center;
    }

    .days {
      --size: 14px;
      display: grid;
      grid-template-columns: auto repeat(var(--weeks), auto) auto;
      grid-template-rows: auto repeat(7, auto);
      gap: 5px;
    }

    .controls {
      font-size: var(--sl-font-size-small);
    }

    .day-of-week {
      font-size: var(--sl-font-size-small);
      display: flex;
      align-items: center;
      height: var(--size);
    }

    .month-of-year {
      font-size: var(--sl-font-size-small);
      width: var(--size);
    }

    [not-last-selected] {
      scale: 0.8;
    }

    .days:has(.day.selected) .day:not(.selected) {
    }

    day-outcome {
      cursor: pointer;
    }

    day-outcome:hover {
      filter: brightness(0.8);
    }
  `];
}