import { Matcher } from '@flakiness/server/common/fql/matcher.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { ContextType, consume } from '@lit/context';
import { LitElement, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { contexts } from '../contexts.js';

@customElement('report-outcomes')
class ReportOutcomes extends LitElement {
  @property({ attribute: false }) outcomes?: WireTypes.OutcomesCount;
  @property({ type: <PERSON>olean, attribute: 'hide-regressions' }) hideRegressions?: boolean;

  @consume({ context: contexts.fql, subscribe: true }) private _fql?: ContextType<typeof contexts.fql>;
  @consume({ context: contexts.linkFQL, subscribe: true }) private _link?: ContextType<typeof contexts.linkFQL>;

  private _renderRate(name: string, from: number, total: number) {
    const rate = total === 0 ? 0 : Math.round(from / total * 10000) / 100;
    return html`${rate}% ${name} rate (${from}/${total})`;
  }

  private _renderChip() {

  }

  override render() {
    const {
      regressed = 0,
      unexpected = 0,
      expected = 0,
      skipped = 0,
      flaked = 0
    } = this.outcomes ?? {};
    const total = regressed + expected + unexpected + skipped + flaked;

    const fql = this._fql;
    const link = this._link;
    
    function statusLink(outcome: WireTypes.Outcome) {
      if (!fql || !link)
        return nothing;
      const q = fql.isMatchingFilter(Matcher.outcomes[outcome]) ? fql.clearStatusFilters() : fql.clearStatusFilters().toggleFilter(Matcher.outcomes[outcome]);
      return link.render(q.serialize());
    }

    const isUnexpected = fql && fql.hasStatusFilters() && fql.acceptsOutcome('unexpected');
    const isExpected = fql && fql.hasStatusFilters() && fql.acceptsOutcome('expected');
    const isSkipped = fql && fql.hasStatusFilters() && fql.acceptsOutcome('skipped');
    const isFlaked = fql && fql.hasStatusFilters() && fql.acceptsOutcome('flaked');
    const isRegressed = fql && fql.hasStatusFilters() && fql.acceptsOutcome('regressed');

    return html`
      <h-box>
        <a class=chip ?selected=${isExpected} href=${statusLink('expected')}>
          <div class=pass-title>Passed</div>
          <div
            class=pass-value
            ?empty=${!expected}
            ?no-color=${unexpected + flaked > 0}
          >${expected}</div>
          <div class=pass-hint>${this._renderRate('pass', expected, total)}</div>
        </a>
        ${this.hideRegressions ? nothing : html`
          <a class=chip ?selected=${isRegressed} href=${statusLink('regressed')}>
            <div class=regressed-title>New Failures</div>
            <div
              class=regressed-value
              ?empty=${!regressed}
            >${regressed}</div>
            <div class=regressed-hint>${this._renderRate('regressed', regressed, total)}</div>
          </a>
        `}
        <a class=chip ?selected=${isUnexpected} href=${statusLink('unexpected')}>
          <div class=fail-title>Failed</div>
          <div
            class=fail-value
            ?empty=${!unexpected}
          >${unexpected}</div>
          <div class=fail-hint>${this._renderRate('failure', unexpected, total)}</div>
        </a>
        <a class=chip ?selected=${isFlaked} href=${statusLink('flaked')}>
          <div class=flake-title>Flaked</div>
          <div
            class=flake-value
            ?empty=${!flaked}
          >${flaked}</div>
          <div class=flake-hint>${this._renderRate('flaked', flaked, total)}</div>
        </a>
        <a class=chip ?selected=${isSkipped} href=${statusLink('skipped')}>
          <div class=skip-title>Skipped</div>
          <div
            class=skip-value
            ?empty=${!skipped}
          >${skipped}</div>
          <div class=skip-hint>${this._renderRate('skipped', skipped, total)}</div>
        </a>
      </h-box>
    `;
  }

  static styles = [css`
    :host {
      display: block;
    }

    a {
      text-decoration: none;

      &[href]:hover {
        text-decoration: none;
      }

      &:visited, &:link {
        color: inherit;
      }
    }

    .chip {
      padding: var(--sl-spacing-2x-small);
      display: flex;
      flex-direction: column;
      gap: var(--sl-spacing-3x-small);
    }

    .pass-title { }
    .pass-value {
      color: var(--fk-color-outcome-expected);
    }
    .pass-hint { }

    .regressed-title { }
    .regressed-value { color: var(--fk-color-outcome-regressed); }
    .regressed-hint { }

    .fail-title { }
    .fail-value { color: var(--fk-color-outcome-unexpected); }
    .fail-hint { }

    .skip-title { }
    .skip-value { color: color-mix(in oklch, var(--fk-color-outcome-skipped) 50%, black 50%); }
    .skip-hint { }

    .flake-title { }
    .flake-value { color: var(--fk-color-outcome-flaked); }
    .flake-hint { }

    [no-color] {
      color: unset;
    }

    [empty] {
      color: var(--sl-color-neutral-200);
    }

    [selected] {
      border-radius: var(--sl-border-radius-small);
      background-color: color-mix(in oklch, yellow , white 70%);
    }

    .pass-title, .fail-title, .skip-title, .flake-title, .regressed-title {
      font-size: var(--sl-font-size-medium);
      font-weight: var(--sl-font-weight-semibold);
    }

    .pass-value, .fail-value, .skip-value, .flake-value, .regressed-value {
      font-size: var(--sl-font-size-2x-large);
    }

    .pass-hint, .fail-hint, .skip-hint, .flake-hint, .regressed-hint {
      font-size: var(--sl-font-size-small);
      color: var(--sl-color-neutral-500);
    }
  `];
}