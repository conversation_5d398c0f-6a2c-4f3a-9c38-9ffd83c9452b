import { wireOutcomesToOutcome, WireTypes } from '@flakiness/server/common/wireTypes.js';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { LinkRenderer } from '../contexts.js';
import { linkStyles } from './cssstyles.js';

export type RunStatsSelectedEvent = CustomEvent<{ runStats: WireTypes.RunStats }>;

@customElement('runstats-selector')
class RunStatsSelector extends LitElement {
  @property({ attribute: false }) runlinker?: LinkRenderer<WireTypes.RunStats>;
  @property({ attribute: false }) runs?: WireTypes.RunStats[];
  @property({ attribute: false }) outcome?: WireTypes.Outcome;
  @property({ type: String }) size: string = 'medium';

  render() {
    const runs = this.runs;
    if (!runs || !runs.length)
      return nothing;

    return html`
      <sl-dropdown hoist>
        <sl-button size=${this.size} slot="trigger" caret>
          <fk-outcome slot=prefix .outcome=${this.outcome}></fk-outcome>
          ${runs.length} ${runs.length === 1 ? 'run' : 'runs'}
        </sl-button>
        <sl-menu>
          ${runs.map(run => html`
            <a href=${this.runlinker?.render(run) ?? nothing}><sl-menu-item .run=${run}>${renderRun(run)}</sl-menu-item></a>
          `)}
        </sl-menu>
      </sl-dropdown>
    `;
  }

  static styles = [linkStyles, css`
    sl-menu a:hover {
      text-decoration: none !important;
    }
  `];
}

function renderRun(run: WireTypes.RunStats) {
  const outcome = wireOutcomesToOutcome(run.testStatsOutcomes)
  return html`
    <fk-outcome slot=prefix .outcome=${outcome}></fk-outcome>
    <span>Run #${run.run.runId}</span>
    <h-box slot=suffix style="gap: var(--sl-spacing-2x-small); font-size: var(--sl-font-size-small); color: var(--sl-color-neutral-500);">
      ${run.hasImages ? html`<artifact-icon .type=${'image'}></artifact-icon>` : nothing}
      ${run.hasVideos ? html`<artifact-icon .type=${'video'}></artifact-icon>` : nothing}
      ${run.hasTraces ? html`<artifact-icon .type=${'pw-trace'}></artifact-icon>` : nothing}
    <h-box>
  `;
}