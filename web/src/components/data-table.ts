
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { Task } from '@lit/task';
import { css, html, LitElement, nothing, TemplateResult } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { assert } from '../utils.js';
import { linkStyles } from './cssstyles.js';

export interface DataColumn<T, K extends string> {
  renderHeader(): TemplateResult;
  renderElement(element: T): TemplateResult|typeof nothing;
  // css grid column width; will be used in grid-template-columns. Defaults to "auto".
  width?: string;
  sortAxis: K|undefined;
  // This defaults to [desc, asc]. For some columns though, the natural sort
  // order should be [desc, asc]. The arrow pointing towards sort direction is actually the
  // "natural sort order" - so it aligns with the first element of sort order.
  sortOrder?: [WireTypes.SortDirection, WireTypes.SortDirection];
}

export type DataTableLinker = (options: {
  page?: number,
  pageSize?: number,
  sortName?: string,
  sortDirection?: WireTypes.SortDirection,
}) => string|undefined;

export type DataLoader<RowDataType, SortType extends string> = (pageOptions: WireTypes.PageOptions, sortOptions: WireTypes.SortOptions<SortType>|undefined, signal: AbortSignal) => Promise<WireTypes.PagedResponse<RowDataType>>
/**
 * Data provider for a table.
 * - The <T> is the type for the rows.
 * - The <K> is the name of the sorting names.
 */
export type DataTableProvider<RowDataType, SortType extends string> = {
  columns: DataColumn<RowDataType, SortType>[];
  defaultSortColumn?: DataColumn<RowDataType, SortType>;
  noDataMessage?: string;
  loader: DataLoader<RowDataType, SortType>;
}

function getSortOrder(column: DataColumn<unknown, string>): [WireTypes.SortDirection, WireTypes.SortDirection] {
  return column.sortOrder && column.sortOrder.length === 2 ? column.sortOrder : ['desc', 'asc'];
}

function toggleSortDirection(column: DataColumn<unknown, string>, sortDirection: WireTypes.SortDirection|undefined) {
  const sortSwitchOrder = getSortOrder(column);
  if (!sortDirection)
    return sortSwitchOrder[0];
  if (sortDirection === sortSwitchOrder[0])
    return sortSwitchOrder[1];
  return sortSwitchOrder[0];
}

@customElement('data-table')
class DataTable extends LitElement {
  @property({ attribute: false }) provider?: DataTableProvider<unknown, string>;
  @property({ attribute: false }) sortAxis?: string;
  @property({ attribute: false }) sortDirection?: WireTypes.SortDirection;
  @property({ attribute: false }) pageNumber?: number;
  @property({ attribute: false }) pageSize?: number;
  @property({ attribute: false }) linker?: DataTableLinker;
  @property({ attribute: false }) hideHeader?: boolean;
  
  private _data = new Task(this, {
    args: () => [
      this.provider, this.sortAxis, this.sortDirection, this.pageNumber, this.pageSize,
    ] as const,
    task: async ([provider, sortAxis, sortDirection, pageNumber, pageSize], { signal }) => {
      assert(provider && pageNumber !== undefined && pageSize !== undefined);

      let sortOptions = sortAxis && sortDirection ? {
        axis: sortAxis,
        direction: sortDirection,
      } : undefined;
      if (!sortOptions && provider.defaultSortColumn && provider.defaultSortColumn.sortAxis) {
        sortOptions = {
          axis: provider.defaultSortColumn.sortAxis,
          direction: toggleSortDirection(provider.defaultSortColumn, undefined),
        };
      }
  
      const page = await provider.loader(
        { number: pageNumber, size: pageSize },
        sortOptions,
        signal
      );
      return { provider, page };
    },
  });

  private _renderPager(currentPage: number, totalPages: number): TemplateResult[] {
    // we render 3 first pages, 5 pages around current page, and 3 last pages.
    const pageNumbers = new Set<number>();
    for (let p = 0; p < Math.min(totalPages, 3); ++p)
      pageNumbers.add(p);
    for (let p = Math.max(totalPages - 3, 0); p < totalPages; ++p)
      pageNumbers.add(p);
    for (let p = Math.max(currentPage - 2, 0); p <= Math.min(currentPage + 2, totalPages - 1); ++p)
      pageNumbers.add(p);
    const elements: TemplateResult[] = [];
    const sorted = [...pageNumbers].sort((a, b) => a - b);
    if (sorted.length === 1)
      return [];
    for (let i = 0; i < sorted.length; ++i) {
      if (i > 0 && sorted[i] - sorted[i - 1] > 1)
        elements.push(html`...`);
      const link = this.linker?.call(null, { page: sorted[i] });
      elements.push(html`
        <a
          class=${classMap({page: true, current: sorted[i] === currentPage })}
          @click=${link ? nothing : () => {
            this.pageNumber = sorted[i];
          }}
          href=${link ?? nothing}>${sorted[i] + 1}</a>
      `);
    }
    return elements;
  }

  render() {
    if (!this._data.value || this._data.value.page.elements.length === 0) {
      return this._data.render({
        pending: () => html`<sl-spinner></sl-spinner>`,
        complete: () => html`
          <div id=no-data>
            <h1>${this._data.value?.provider.noDataMessage ?? 'No Data'}</h1>
          </div>
        `,
        error: () => nothing,
      });
    }
    const provider = this._data.value.provider;
    const columns = provider.columns;
    const defaultSortColumn = provider.defaultSortColumn;
    const page = this._data.value.page;

    let currentSortAxis = this.sortAxis;
    let currentSortDirection = this.sortDirection;
    let sortColumn = columns.find(column => column.sortAxis === currentSortAxis);
    if (defaultSortColumn && (!currentSortDirection || !currentSortAxis || !sortColumn)) {
      sortColumn = defaultSortColumn;
      currentSortAxis = defaultSortColumn.sortAxis;
      currentSortDirection = toggleSortDirection(defaultSortColumn, undefined);
    }

    const renderPagerRow = () => html`
      <h-box>
        <span class=pager-description>Showing ${page.pageNumber*page.pageSize + 1}-${Math.min((page.pageNumber + 1)*page.pageSize, page.totalElements)} of ${page.totalElements}</span>
        <x-filler></x-filler>
        ${page.totalPages > 1 ? html`
          <div class=paging>
            ${this._renderPager(page.pageNumber, page.totalPages)}
          </div>
        ` : nothing}
      </h-box>
    `;

    return html`
      <v-box style="gap: var(--sl-spacing-3x-small); height: 100%;">
        ${renderPagerRow()}
        <section style="position: relative; --columns: ${columns.map(c => c.width ?? 'auto').join(' ')};">
          ${this._data.render({
            pending: () => html`<div class=loading></div>`,
          })}
          ${this.hideHeader ? nothing : html`
            <div class="row header-row" part=header>
            ${columns.map(column => this._renderColumnHeader(column, sortColumn, currentSortDirection))}
            </div>
          `}
          ${page.elements.map((element) => html`
            <div class="row">
              ${columns.map(column => html`
                <div class=cell>${column.renderElement(element)}</div>
              `)}
            </div>
          `)}
        </section>
        ${renderPagerRow()}
      </v-box>
    `;
  }

  private _renderColumnHeader(column: DataColumn<unknown, string>, sortColumn: DataColumn<unknown, string> | undefined, currentSortDirection: WireTypes.SortDirection | undefined) {
    const isSortable = !!column.sortAxis;
    if (!isSortable)
      return html`<div class=cell>${column.renderHeader()}</div>`;
    let sortIcon: TemplateResult|undefined;
    let newSortDirection: WireTypes.SortDirection;
    if (column === sortColumn) {
      newSortDirection = toggleSortDirection(column, currentSortDirection);
      const isPrimaryOrder = getSortOrder(sortColumn)[0] === currentSortDirection;
      if (isPrimaryOrder)
        sortIcon = html`<sl-icon name=arrow-down-short></sl-icon>`;
      else
        sortIcon = html`<sl-icon name=arrow-up-short></sl-icon>`;
    } else {
      newSortDirection = toggleSortDirection(column, undefined);
      sortIcon = html`<sl-icon></sl-icon>`;
    }
    const link = this.linker?.({ sortName: column.sortAxis, sortDirection: newSortDirection, page: undefined, });
    return html`
      <a
        href=${link ?? nothing}
        @click=${link ? nothing : () => {
          this.sortAxis = column.sortAxis;
          this.sortDirection = newSortDirection;
        }}
        class=cell
        sortable
      >${column.renderHeader()}${sortIcon}
      </a>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      display: block;
    }

    #no-data {
      display: flex;
      align-items: center;
      color: var(--fk-color-border);
    }

    sl-icon {
      flex: none;
    }

    .pager-description {
      color: var(--sl-color-neutral-600);
      font-size: var(--sl-font-size-small);
      margin: var(--sl-spacing-small) 0;
      margin-left: var(--sl-spacing-2x-small);
    }

    .paging {
      font-size: var(--sl-font-size-small);
      user-select: none;

      .page {
        padding: var(--sl-spacing-2x-small) var(--sl-spacing-small);
        cursor: pointer;
  
        &.current {
          background-color: var(--sl-color-neutral-100);
          border-radius: var(--sl-border-radius-medium);
          font-weight: var(--sl-font-weight-semibold);
        }
  
        &:hover {
          text-decoration: none !important;
        }
  
        &:not(.active):hover {
          background-color: var(--sl-color-neutral-50);
        }
      }
    }

    a[sortable] {
      cursor: pointer;
    }
    
    section {
      display: grid;
      gap: 0 0px;
      grid-template-columns: var(--columns);
      border: 1px solid var(--fk-color-border);
      border-radius: var(--sl-border-radius-large);
      overflow: auto;

      & > div.row {
        display: grid;
        grid-template-columns: subgrid;
        grid-template-rows: auto;
        grid-column: 1/-1;
        padding: 0 var(--sl-spacing-small);
        border-bottom: 1px solid var(--fk-color-border);
        column-gap: var(--sl-spacing-small);

        &:not(.header-row):hover {
          background-color: var(--fk-color-tablerow-hover);
        }

        &.header-row {
          background-color: var(--sl-color-neutral-50);  
          border-bottom: 1px solid var(--fk-color-border);  
          font-size: var(--sl-font-size-small);
          font-weight: var(--sl-font-weight-bold);
          padding: var(--sl-spacing-small);
        }

        &.header-row > .cell > sl-icon {

        }

        & > .cell {
          white-space: nowrap;
          text-overflow: ellipsis;
          display: flex;
          align-items: center;
          min-width: 0;
          overflow: hidden;
        }
      }
    }

    .loading {
      position: absolute;
      inset: 0;
      --loading-color: rgb(33, 150, 243);
      background: linear-gradient(
        135deg,
        color-mix(in srgb, var(--loading-color) 30%, transparent) 0%,
        color-mix(in srgb, var(--loading-color) 15%, transparent) 12.5%,
        color-mix(in srgb, var(--loading-color) 30%, transparent) 25%,
        color-mix(in srgb, var(--loading-color) 15%, transparent) 37.5%,
        color-mix(in srgb, var(--loading-color) 30%, transparent) 50%,
        color-mix(in srgb, var(--loading-color) 15%, transparent) 62.5%,
        color-mix(in srgb, var(--loading-color) 30%, transparent) 75%,
        color-mix(in srgb, var(--loading-color) 15%, transparent) 87.5%,
        color-mix(in srgb, var(--loading-color) 30%, transparent) 100%
      );
      background-size: 200% 200%;
      background-position: 0% 0%;
      animation: shimmer 3s linear infinite, fadeIn 1000ms ease-in forwards;
      opacity: 0;
      pointer-events: none;
      z-index: 10;
    }

    sl-spinner {
      opacity: 0;
      animation: fadeIn 1000ms ease-in forwards;
    }

    @keyframes shimmer {
      0% {
        background-position: 0% 0%;
      }
      100% {
        background-position: 100% 100%;
      }
    }

    @keyframes fadeIn {
      to {
        opacity: 1;
      }
    }
  `]
}