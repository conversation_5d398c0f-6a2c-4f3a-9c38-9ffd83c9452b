import { wireOutcomesToOutcome, WireTypes } from '@flakiness/server/common/wireTypes.js';
import { SlPopup } from '@shoelace-style/shoelace';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, query, state } from 'lit/decorators.js';
import { consumeDOMEvent } from '../utils.js';
import { linkStyles } from './cssstyles.js';
import { DurationsChart2, DurationsChartDatum } from './durations-chart2.js';

type Datum = DurationsChartDatum & {
  items: {
    stats: WireTypes.CommitStats,
    baseline?: number,
  }[];
}

@customElement('commits-minimap')
export class CommitsMinimap extends LitElement {
  @property({ attribute: false }) header?: string;
  @property({ attribute: false }) commitStats?: WireTypes.CommitStats[];

  @state() private _data: (Datum|{ separator: string })[] = [];
  @query('sl-popup') _popup?: SlPopup;

  protected override update(changedProperties: Map<string, any>): void {
    if (changedProperties.has('commitStats')) {
      const commitStats = this.commitStats ?? [];
      const predata: Datum[] = commitStats.map(s => ({
        outcome: wireOutcomesToOutcome(s.testStats),
        duration: s.runs.length ? s.durationMs : undefined,
        items: [{
          stats: s,
        }],
      }));
      let previousDuration: number|undefined;
      for (const d of predata) {
        if (!d.duration)
          continue;
        if (previousDuration)
          d.items[0].baseline = previousDuration;
        previousDuration = d.duration;
      }
      const data : Datum[]= [];
      for (const datum of predata.toReversed()) {
        if (!data.length || datum.outcome !== undefined)
          data.push(datum);
        else if (datum.outcome === undefined)
          data.at(-1)!.items.push(...datum.items);
      }
      for (const datum of data)
        datum.items.reverse();
      data.reverse();
      // Add separators
      this._data = [];
      let lastDay: string|undefined;
      for (const datum of data) {
        const date = new Date(datum.items.at(-1)!.stats.commit.timestamp);
        const day = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        if (day !== lastDay)
          this._data.push({ separator: day });
        lastDay = day;
        this._data.push(datum);
      }
    }
    super.update(changedProperties);
  }

  @state() private _hoveredDatum?: Datum;

  private _renderPopover() {
    const stats = this._hoveredDatum?.items ?? [];
    if (!stats.length)
      return nothing;
    const toRender = stats.toReversed().slice(0, 3);

    return html`
      <section class=popup>
        ${toRender.map(({ stats, baseline }) => html`
          <fk-commitstats .stats=${stats} .baseline=${baseline}></fk-commitstats>
        `)}
        ${stats.length > toRender.length ? html`
          <div class=more-commits>
            +${stats.length - toRender.length} more untested commits.
          </div>
        ` : nothing}
      </section>
    `;
  }

  override render() {
    return html`
      <sl-popup ?active=${!!this._hoveredDatum} arrow distance=10 shift>
        ${this._renderPopover()}
      </sl-popup>
      <v-box class=minimap>
        <div class=title>${this.header}</div>
        ${DurationsChart2.render({
          data: this._data,
          fancy: true,
          click: event => {
            this.dispatchEvent(new CustomEvent<WireTypes.CommitStats[]>('fk-select', { detail: event.detail.datum.items.map(i => i.stats) }));
            consumeDOMEvent(event);
          },
          hover: event => {
            this._hoveredDatum = event.detail.datum;
            if (this._popup)
              this._popup.anchor = event.detail.bar;
          },
          leave: () => {
            this._hoveredDatum = undefined;
          }
        })}
      </v-box>
    `;
  }

  static styles = [linkStyles, css`
    .minimap {
    }

    sl-popup {
      --arrow-color: var(--sl-color-neutral-300);
    }

    .popup {
      background: white;
      border: 1px solid var(--sl-color-neutral-300);
      border-radius: var(--sl-border-radius-medium);
      pointer-events: none;
      overflow: hidden;
      margin: 0 var(--sl-spacing-medium);

      .more-commits {
        background: var(--sl-color-neutral-50);
        display: flex;
        align-items: center;
        justify-content: center;
        border-top: 1px solid var(--fk-color-border);
        font-weight: var(--sl-font-weight-semibold);
        padding: var(--sl-spacing-small);
      }

      fk-commitstats {
        grid-column: 1/-1;
        border: none;
        border-radius: 0;
      }

      fk-commitstats + fk-commitstats {
        border-top: 1px solid var(--fk-color-border);
      }

    }

    durations-chart2 {
      height: 60px;
      border-bottom: 1px solid var(--fk-color-border);
    }

    .title {
      font-size: var(--sl-font-size-large);
    }

    .expected {
      --color: var(--fk-color-outcome-expected);
    }
    .flaked {
      --color: var(--fk-color-outcome-flaked);
    }
    .regressed {
      --color: var(--fk-color-outcome-regressed);
    }
    .skipped {
      --color: var(--fk-color-outcome-skipped);
    }
    .unexpected {
      --color: var(--fk-color-outcome-unexpected);
    }
    .idle, .untested {
      --color: transparent;
    }
  `];
}
