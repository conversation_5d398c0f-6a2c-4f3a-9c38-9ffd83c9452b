import { Stats } from '@flakiness/server/common/stats/stats.js';
import { TimelineSplit } from '@flakiness/server/common/timeline/timelineSplit.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { consume, ContextType } from '@lit/context';
import { Task } from '@lit/task';
import { min, timeDay, timeSunday } from 'd3';
import { css, html, LitElement, nothing, PropertyValues } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { styleMap } from 'lit/directives/style-map.js';
import ms from 'ms';
import { api } from '../api.js';
import { contexts } from '../contexts.js';
import { assert, REGRESSION_WINDOW_DAYS, wireDays } from '../utils.js';
import { linkStyles } from './cssstyles.js';
import { DayData, DayStatus } from './github-calendar.js';

export type ExecutionCalendarLinker = (options: {
  since: Date,
  until: Date,
}) => string|undefined;

@customElement('execution-calendar')
export class ExecutionCalendar extends LitElement {
  @property({ attribute: false }) head?: WireTypes.Ref;
  @property({ attribute: false }) split?: TimelineSplit;
  @property({ attribute: false }) testId?: Stats.TestId;
  @property({ attribute: false }) linker?: ExecutionCalendarLinker;
  @property({ attribute: false }) since?: Date;
  @property({ attribute: false }) until?: Date;
  @property({ attribute: false }) singleDay?: boolean;

  @state() private _since?: Date;
  @state() private _until?: Date;

  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;

  private _computeCalendarInterval(date?: Date) {
    if (!date) {
      // If selection is removed, that drop all intervals.
      this._until = undefined;
      this._since = undefined;
    } else if (!this._until || !this._since) {
      // If at least one of the intervals is not defined, then define them.
      this._until = timeSunday.ceil(min([
        timeDay.offset(date, +30),
        new Date()
      ])!);
      this._since = timeSunday.offset(this._until, -12);
    } else if (date < this._since) {
      const shift = timeSunday.count(date, this._since);        
      this._since = timeSunday.offset(this._since, -shift);
      this._until = timeSunday.offset(this._until, -shift);
    } else if (date >= this._until) {
      const shift = timeSunday.count(this._until, date) + 1;        
      this._since = timeSunday.offset(this._since, shift);
      this._until = timeSunday.offset(this._until, shift);
    }
  }

  protected willUpdate(_changedProperties: PropertyValues<this>): void {
    super.willUpdate(_changedProperties);
    if (_changedProperties.has('until') && _changedProperties.get('until')?.getTime() !== this.until?.getTime()) {
      this._computeCalendarInterval(this.until);
    }
    if (_changedProperties.has('since') && _changedProperties.get('since')?.getTime() !== this.since?.getTime()) {
      this._computeCalendarInterval(this.since);
    }
  }

  private _dayData(): DayData[] {
    if (!this._since || !this._until)
      return [];
    const cachedDays = this._cachedDaysTask.value ?? new Map();
    return timeDay.range(this._since, this._until).map((day, dayIdx) => ({
      status: cachedDays.get(+day) ?? 'loading',
      date: day,
    }));
  }

  // This is a smart cache: it caches results per testId x timeline x commitId. 
  private _cachedDaysTask = new Task(this, {
    args: () => [
      // everytime timeline / head changes / testId changes, we have to reset cache.
      this.split?.etag(), this.head?.commit.commitId, this.testId
    ] as const,
    task: async ([split, head, testId], { signal }) => {
      assert(split && head);
      return new Map<number, DayStatus>();
    }
  });

  private _loadDaysTask = new Task(this, {
    args: () => [
      this._cachedDaysTask.value, this._project, this.head?.commit.commitId, this.split, this.testId, this._since?.getTime(), this._until?.getTime()
    ] as const,
    task: async ([cachedDays, project, head, split, testId, sinceTimestamp, untilTimestamp], { signal }) => {
      assert(cachedDays && project && head && split && sinceTimestamp && untilTimestamp);

      // If all data has been loaded, then no need to issue any loading requests.
      const data = this._dayData();
      if (!data.some(d => d.status === 'loading'))
        return;

      const days = wireDays({
        backfillDays: REGRESSION_WINDOW_DAYS,
        since: new Date(sinceTimestamp),
        until: new Date(untilTimestamp),
      });
      const outcomes = await api.report.dailyOutcomes.POST({
        orgSlug: project.org.orgSlug,
        projectSlug: project.projectSlug,
        head,
        days,
        regressionWindowDays: REGRESSION_WINDOW_DAYS,
        resultDays: days.length - REGRESSION_WINDOW_DAYS,
        testId,
        timelineSplit: split.serialize(),
      }, { signal });
      for (let dayIdx = 0; dayIdx < days.length - REGRESSION_WINDOW_DAYS; ++dayIdx) {
        const day = days[dayIdx];
        cachedDays.set(day.sinceTimestamp, outcomes[dayIdx]);
      }
      this.requestUpdate();
    }
  });

  private _selectDay(day: Date) {
    day = timeDay.floor(day);
    let since = this.since;
    let until = this.until;
    if (!since || !until)
      return;
    const delta = +until - +since;
    const newUntil = timeDay.offset(day, 1);
    const newSince = new Date(+newUntil - delta);
    this.since = newSince;
    this.until = newUntil;
    this.dispatchEvent(new CustomEvent<{ since: Date, until: Date }>('rangeselected', {
      detail: {
        since: newSince,
        until: newUntil,
      },
    }));
  }

  render() {
    return html`
      <github-calendar
        .selectedSince=${this.since}
        .selectedUntil=${this.until}
        .data=${this._dayData()}
        @dayselected=${(event: CustomEvent<Date>) => {
          this._selectDay(event.detail);
        }}
        @weekchanged=${(event: CustomEvent<{ since: Date, until: Date }>) => {
          this._since = event.detail.since;
          this._until = event.detail.until;
        }}
      >
        <v-box slot=widgets style=${styleMap({
          'height': '100%',
          'align-items': 'center',
          'gap': 'var(--sl-spacing-2x-small)',
          'margin-left': 'var(--sl-spacing-x-small)',
        })}>
          ${this.singleDay ? nothing : html`
            <head-calendar
              .maxRangeDays=${14}
              .headRef=${this.head}
              .since=${this.since}
              .until=${this.until}
              @fk-select=${(event: CustomEvent<{ since: Date, until: Date }>) => {
                this.since = event.detail.since;
                this.until = event.detail.until;
                this.dispatchEvent(new CustomEvent<{ since: Date, until: Date }>('rangeselected', {
                  detail: event.detail
                }));
              }}
            ></head-calendar>
            ${this._renderRangeSelectionButton(ms('2 weeks'), '2w')}
            ${this._renderRangeSelectionButton(ms('1 week'), '1w')}
            ${this._renderRangeSelectionButton(ms('1 day'), '1d')}
          `}
          <x-filler></x-filler>
          <sl-icon-button name=crosshair @click=${() => {
            this._selectDay(new Date());
          }}></sl-icon-button>
        </v-box>
      </github-calendar>
    `;
  }

  private _renderRangeSelectionButton(intervalMs: number, name: string) {
    const since = this.since;
    const until = this.until;
    const delta = since && until ? (+until - +since) : 0;
    const href = since && until && this.linker ? this.linker({
      since: new Date(+until - intervalMs),
      until,
    }) : nothing;
    return html`
      <a
        href=${href}
        @click=${href ? nothing : () => {

        }}
        class=${classMap({
          'range-selection': true,
          selected: delta === intervalMs,
        })}>${name}</a>
    `;
  }
  
  static styles = [linkStyles, css`
    .range-selection {
      text-decoration: none;
      padding: var(--sl-spacing-3x-small) var(--sl-spacing-2x-small);
      color: var(--sl-color-neutral-600);
      font-family: monospace;

      &:hover {
        color: var(--sl-color-primary-600);
        cursor: pointer;
      }

      &.selected {
        background-color: var(--sl-color-neutral-100);
        border-radius: var(--sl-border-radius-medium);
        outline: 1px solid var(--fk-color-border);
      }
    }
  `];
}
