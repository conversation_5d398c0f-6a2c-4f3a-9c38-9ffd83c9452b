import type { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { linkStyles } from './cssstyles.js';

@customElement('fk-shortcommit')
class FKShortCommit extends LitElement {
  @property({ attribute: false }) commit?: WireTypes.Commit;

  override render() {
    const commit = this.commit;
    if (!commit)
      return nothing;
    const title = commit.message.split('\n')[0].trim();
    return html`
      <sl-avatar image=${commit.avatar_url} label=${commit.author} loading=lazy></sl-avatar>
      <span class=title>${title}</span>
    `
  }

  static styles = [linkStyles, css`
    :host {
      display: inline-block;
    }

    sl-avatar {
      --size: 16px;
    }

    .title {
      font-weight: var(--sl-font-weight-semibold);
      font-size: var(--sl-font-size-medium);
      white-space: nowrap;
    }
  `];
}
