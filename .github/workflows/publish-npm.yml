name: Publish to NPM

on:
  workflow_dispatch:
  push:
    tags:
      - 'v*'  # This will match tags like v1.0.0, v2.1.0, etc.

permissions:
  id-token: write  # Required for OIDC
  contents: read

jobs:
  publish-to-npm:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 24

      # Ensure npm 11.5.1 or later is installed
      - name: Update npm
        run: npm install -g npm@latest

      - name: Build & Publish shared, report & sdk packages
        run: |
          npm ci
          npx kubik ./shared/build.mts ./report/build.mts ./sdk/build.mts
          cd shared && npm publish && cd ..
          cd report && npm publish && cd ..
          cd sdk && npm publish && cd ..
