name: "tests 1"

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

concurrency:
  # For pull requests, cancel all currently-running jobs for this workflow
  # https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#concurrency
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  # Force terminal colors. @see https://www.npmjs.com/package/colors
  FORCE_COLOR: 1
  FLAKINESS_ACCESS_TOKEN: ${{ secrets.FLAKINESS_ACCESS_TOKEN }}
  OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.OP_TEST }}

jobs:
  test_linux:
    name: ${{ matrix.os }} (Node.js ${{ matrix.node-version }})
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-22.04]
        node-version: [24]
    runs-on: ${{ matrix.os }}
    steps:
    - uses: actions/checkout@v4

    - name: Install 1Password CL<PERSON>
      uses: 1password/install-cli-action@v1

    - uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}

    - run: docker pull quay.io/minio/minio:latest
    - run: docker pull postgres:latest
    - run: ./config/config test
    - run: npm ci
    - run: npm run build
    - run: npx playwright install --with-deps
    - name: Database Tests
      run: npm run test
      working-directory: database
      if: always()
    - name: Shared Tests
      run: npm run test
      working-directory: shared
      if: always()
    - name: Server Tests
      run: npm run test
      working-directory: server
      if: always()
    - name: E2E Tests
      run: npm run test
      working-directory: e2e
      if: always()
    - name: Demo Tests
      run: npx playwright test
      working-directory: demotests
      if: always()
    - name: Demo Tests - 2
      run: npx playwright test
      env:
        FAIL_THAT_TEST_THAT_FAILS_ON_A_SECOND_RUN: 1
      working-directory: demotests
      if: always()

