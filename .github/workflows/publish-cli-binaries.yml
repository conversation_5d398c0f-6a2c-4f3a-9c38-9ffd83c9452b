name: Publish CLI

on:
  workflow_dispatch:
  push:
    tags:
      - 'v*'  # This will match tags like v1.0.0, v2.1.0, etc.

env:
  OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.OP_PROD_CI_USE_WITH_EXTREME_CAUTION }}

jobs:
  publish-to-s3-bucket:
    runs-on: ubuntu-latest

    steps:
      - name: Install 1Password CLI
        uses: 1password/install-cli-action@v1

      - name: Check 1Password CLI User
        run: op user get --me

      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 24

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2

      - name: Setup rclone
        run:
          curl https://rclone.org/install.sh | sudo bash

      # Ensure npm 11.5.1 or later is installed
      - name: Update npm
        run: npm install -g npm@latest

      - name: Build CLI tool
        run: |
          npm ci
          npx kubik ./sdk/build.mts
          ./sdk/deployment/build.sh all

      - name: Upload to S3 bucket
        run: |
          ./config/config deploymentsecrets
          ./sdk/deployment/upload.sh
          rm -rf .env.deploymentsecrets

  test-cli-installation:
    needs: publish-to-s3-bucket
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
    runs-on: ${{ matrix.os }}

    steps:
      - name: Install CLI (Linux/macOS)
        if: runner.os != 'Windows'
        run: curl -LsSf https://cli.flakiness.io/install.sh | sh

      - name: Install CLI (Windows)
        if: runner.os == 'Windows'
        run: pwsh -c "irm https://cli.flakiness.io/install.ps1 | iex"

      - name: Test CLI
        run: flakiness --help
